# AI Assistant Rules Loader for Windows PowerShell
# Fixed version without encoding issues

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "AI Assistant Rules Loader"
    Write-Host "Usage: .\load-ai-rules-fixed.ps1"
    exit 0
}

$RulesFile = "INTELLIGENT_ASSISTANT_RULES.md"

function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Test-RulesFile {
    if (-not (Test-Path $RulesFile)) {
        Write-ColorMessage "Error: Rules file $RulesFile not found" "Red"
        Write-ColorMessage "Please ensure you run this script in the directory containing the rules file" "Yellow"
        exit 1
    }
    Write-ColorMessage "Found rules file: $RulesFile" "Green"
}

function Install-ClaudeCode {
    Write-ColorMessage "Configuring Claude Code..." "Blue"
    
    $ClaudeDir = "$env:USERPROFILE\.claude"
    if (-not (Test-Path $ClaudeDir)) {
        New-Item -ItemType Directory -Path $ClaudeDir -Force | Out-Null
    }
    
    Copy-Item $RulesFile "$ClaudeDir\CLAUDE.md"
    Copy-Item $RulesFile ".\CLAUDE.md"
    
    Write-ColorMessage "Claude Code rules loaded (global + project)" "Green"
}

function Install-Cursor {
    Write-ColorMessage "Configuring Cursor..." "Blue"
    
    Copy-Item $RulesFile ".\.cursorrules"
    
    $CursorDir = "$env:USERPROFILE\.cursor"
    if (Test-Path $CursorDir) {
        Copy-Item $RulesFile "$CursorDir\rules.md"
        Write-ColorMessage "Cursor rules loaded (global + project)" "Green"
    } else {
        Write-ColorMessage "Cursor project rules loaded" "Green"
    }
}

function Install-VSCode {
    Write-ColorMessage "Configuring VS Code..." "Blue"
    
    if (-not (Test-Path ".vscode")) {
        New-Item -ItemType Directory -Path ".vscode" -Force | Out-Null
    }
    
    $VSCodeSettings = '{
  "mcp": {
    "servers": {
      "context7": {
        "command": "npx",
        "args": ["-y", "@upstash/context7-mcp@latest"]
      }
    }
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  }
}'
    
    $VSCodeSettings | Out-File -FilePath ".vscode\settings.json" -Encoding UTF8
    
    if (-not (Test-Path ".continue")) {
        New-Item -ItemType Directory -Path ".continue" -Force | Out-Null
    }
    
    $ContinueConfig = '{
  "models": [
    {
      "title": "Claude 3.5 Sonnet",
      "provider": "anthropic",
      "model": "claude-3-5-sonnet-20241022",
      "systemMessage": "Follow the rules in INTELLIGENT_ASSISTANT_RULES.md"
    }
  ]
}'
    
    $ContinueConfig | Out-File -FilePath ".continue\config.json" -Encoding UTF8
    
    Write-ColorMessage "VS Code configuration created" "Green"
}

function Install-ClaudeDesktop {
    Write-ColorMessage "Checking Claude Desktop configuration..." "Blue"
    
    $ClaudeConfigFile = "$env:APPDATA\Claude\claude_desktop_config.json"
    
    if (Test-Path $ClaudeConfigFile) {
        Write-ColorMessage "Found Claude Desktop config file" "Green"
        Write-ColorMessage "Please manually add custom instructions in Claude Desktop" "Yellow"
    } else {
        Write-ColorMessage "Claude Desktop config file not found" "Yellow"
    }
}

function Install-OtherPlatforms {
    Write-ColorMessage "Creating other platform rules..." "Blue"
    
    Copy-Item $RulesFile ".\.windsurfrules"
    Copy-Item $RulesFile ".\.clinerules"
    Copy-Item $RulesFile ".\AI_RULES.md"
    
    Write-ColorMessage "Other platform rules created" "Green"
}

function Test-Installation {
    Write-ColorMessage "Verifying installation..." "Blue"
    
    $FilesCreated = 0
    
    $Files = @(
        "CLAUDE.md",
        ".cursorrules",
        ".vscode\settings.json",
        ".continue\config.json",
        ".windsurfrules",
        ".clinerules",
        "AI_RULES.md"
    )
    
    foreach ($File in $Files) {
        if (Test-Path $File) {
            Write-ColorMessage "  Found: $File" "Green"
            $FilesCreated++
        }
    }
    
    Write-ColorMessage "Installation complete! Created $FilesCreated configuration files" "Green"
}

function Show-Usage {
    Write-ColorMessage "Usage Instructions:" "Blue"
    Write-Host ""
    Write-ColorMessage "1. Claude Code:" "Yellow"
    Write-Host "   - Global rules: $env:USERPROFILE\.claude\CLAUDE.md"
    Write-Host "   - Project rules: .\CLAUDE.md"
    Write-Host ""
    Write-ColorMessage "2. Cursor:" "Yellow"
    Write-Host "   - Project rules: .\.cursorrules"
    Write-Host ""
    Write-ColorMessage "3. VS Code:" "Yellow"
    Write-Host "   - Workspace settings: .vscode\settings.json"
    Write-Host "   - Continue.dev: .continue\config.json"
    Write-Host ""
    Write-ColorMessage "Test command:" "Blue"
    Write-Host "   Ask your AI assistant: 'What development rules are you following?'"
}

function Main {
    Write-ColorMessage "AI Assistant Rules Loader" "Green"
    Write-ColorMessage "=========================" "Green"
    Write-Host ""
    
    Test-RulesFile
    Write-Host ""
    
    Install-ClaudeCode
    Install-Cursor
    Install-VSCode
    Install-ClaudeDesktop
    Install-OtherPlatforms
    Write-Host ""
    
    Test-Installation
    Write-Host ""
    
    Show-Usage
    Write-Host ""
    
    Write-ColorMessage "All AI assistant rules have been loaded successfully!" "Green"
    Write-ColorMessage "Please restart your AI assistant applications" "Yellow"
}

try {
    Main
} catch {
    Write-ColorMessage "An error occurred: $($_.Exception.Message)" "Red"
    exit 1
}
