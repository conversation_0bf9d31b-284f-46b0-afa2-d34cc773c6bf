# 🚀 智能开发助手规则文件

> 基于2025年最新AI技术和开发最佳实践的全面规则文件，让您的AI助手更智能、更权威、更高效！

## 📁 文件说明

| 文件名 | 描述 | 用途 |
|--------|------|------|
| `INTELLIGENT_ASSISTANT_RULES.md` | 🎯 主规则文件 | 包含完整的AI助手行为规则和最佳实践 |
| `RULES_LOADING_GUIDE.md` | 📖 加载指南 | 详细的手动加载说明 |
| `load-ai-rules.sh` | 🐧 Linux/macOS脚本 | 一键自动加载规则到各个平台 |
| `load-ai-rules-fixed.ps1` | 🪟 Windows脚本 | PowerShell版本的自动加载脚本 |
| `simple-system-update.ps1` | 🔧 系统更新脚本 | 检测和更新系统信息 |
| `test-ai-simple.ps1` | 🧪 测试脚本 | 验证AI助手配置和功能 |

## 🚀 快速开始

### Windows用户

1. **运行加载脚本**
```powershell
# 在PowerShell中执行
.\load-ai-rules-fixed.ps1
```

2. **验证加载结果**
```powershell
# 检查配置是否正确
.\test-ai-simple.ps1
```

3. **更新系统信息**
```powershell
# 更新系统环境信息
.\simple-system-update.ps1
```

### Linux/macOS用户

1. **运行加载脚本**
```bash
# 给脚本执行权限
chmod +x load-ai-rules.sh

# 执行加载脚本
./load-ai-rules.sh
```

2. **验证加载结果**
```bash
# 检查创建的文件
ls -la | grep -E "\.(cursorrules|clinerules|windsurfrules)"
ls -la .vscode/ .continue/
```

## 🎯 支持的AI助手平台

### ✅ 完全支持
- **Claude Code** - 命令行AI助手
- **Cursor** - AI代码编辑器
- **VS Code + Continue.dev** - VS Code AI扩展
- **Windsurf** - AI开发环境
- **Cline** - VS Code AI助手扩展

### 🔧 需要手动配置
- **Claude Desktop** - 桌面版Claude应用
- **GitHub Copilot** - 通过VS Code设置
- **ChatGPT** - 通过自定义指令

## 📋 规则文件功能特性

### 🎯 核心功能
- ✅ **终端命令自动化** - 安全的只读操作自动执行
- ✅ **智能参数处理** - 环境变量和配置文件智能解析
- ✅ **MCP协议集成** - 模型上下文协议完整支持
- ✅ **主动问题解决** - 预测性分析和智能调试
- ✅ **权威性决策** - 基于最佳实践的明确指导

### 🚀 高级功能
- 🤖 **AI代码生成** - 智能代码生成和重构
- 🔮 **预测性维护** - 代码健康度预测
- 🔒 **高级安全** - 安全审查和合规检查
- 📊 **数据驱动** - 性能分析和优化建议
- 🎨 **现代技术栈** - 2025年最新开发工具支持

## 🧪 测试AI助手

加载规则后，使用以下命令测试AI助手是否正确工作：

```
1. 请告诉我你当前遵循的开发规则和最佳实践
2. 请帮我检查当前项目的健康状态
3. 请列出可用的MCP服务器
4. 请分析这个项目的技术栈并给出优化建议
5. 请执行 'npm list' 命令并分析依赖情况
```

### 预期响应
AI助手应该能够：
- 🎯 引用规则文件中的具体内容
- 🔧 主动执行安全的只读操作
- 💡 提供基于最佳实践的建议
- 🌐 展示MCP服务器集成能力
- 📊 进行智能分析和预测

## 📁 创建的配置文件

运行加载脚本后，将创建以下文件：

```
项目目录/
├── CLAUDE.md                    # Claude Code 项目规则
├── .cursorrules                 # Cursor 规则文件
├── .windsurfrules              # Windsurf 规则文件
├── .clinerules                 # Cline 规则文件
├── AI_RULES.md                 # 通用规则文件
├── .vscode/
│   └── settings.json           # VS Code 工作区设置
└── .continue/
    └── config.json             # Continue.dev 配置

全局配置/
├── ~/.claude/CLAUDE.md         # Claude Code 全局规则
├── ~/.cursor/rules.md          # Cursor 全局规则 (如果目录存在)
└── Claude Desktop配置          # 需要手动配置
```

## 🔧 自定义配置

### 环境变量设置
```bash
# 在 .env 文件中设置必要的API密钥
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
GITHUB_TOKEN=ghp_...
```

### MCP服务器配置
规则文件包含以下MCP服务器的配置：
- **Context7** - 上下文检索
- **Peekaboo** - 多模态AI
- **GitHub** - Git集成
- **Playwright** - 浏览器自动化
- **Filesystem** - 文件系统访问

## 🔄 更新规则

### 自动更新
```bash
# 下载最新规则文件
curl -o INTELLIGENT_ASSISTANT_RULES.md https://raw.githubusercontent.com/your-repo/rules/main/INTELLIGENT_ASSISTANT_RULES.md

# 重新加载
./load-ai-rules.sh  # Linux/macOS
.\load-ai-rules.ps1  # Windows
```

### 手动更新
1. 下载最新的规则文件
2. 重新运行加载脚本
3. 重启AI助手应用

## 🐛 故障排除

### 常见问题

**Q: 规则没有生效？**
A: 
- 检查文件路径是否正确
- 重启AI助手应用
- 验证文件权限和格式

**Q: MCP服务器连接失败？**
A:
- 检查网络连接
- 验证API密钥配置
- 查看AI助手的错误日志

**Q: 脚本执行失败？**
A:
- 确保在正确的目录中运行
- 检查PowerShell执行策略 (Windows)
- 验证文件权限 (Linux/macOS)

### 获取帮助
- 📖 查看 `RULES_LOADING_GUIDE.md` 详细说明
- 🔍 运行 `verify-rules-loading.ps1` 诊断问题
- 💬 在项目Issues中报告问题

## 📄 许可证

MIT License - 自由使用、修改和分发

---

**🎉 享受更智能的AI助手开发体验！**

*基于2025年最新AI技术和开发最佳实践 | 持续更新中*

---

## 📝 仓库说明

这个仓库包含了完整的AI助手规则文件和配置工具，旨在为开发者提供智能、高效的AI辅助开发体验。

### 🎯 主要特性
- 🤖 智能终端命令自动化
- 🔧 MCP协议完整集成
- 🚀 预测性问题解决
- 💡 权威性技术决策
- 🌐 跨平台AI助手支持

### 📊 系统要求
- Windows 10/11 或 macOS/Linux
- PowerShell 5.1+ 或 Bash
- Node.js 20+ (可选)
- Git 2.0+

**🎊 让您的AI助手更智能！**
