"""
Galaxy Ticketing 极速抢票配置文件
优化版本 - 专注于速度和性能
"""

# 目标网站配置
TARGET_URL = "https://www.galaxyticketing.com/#/allEvents/detail/selectTicket?activityId=50000000740007"
BASE_URL = "https://www.galaxyticketing.com"

# 速度优化配置
SPEED_CONFIG = {
    # 页面加载超时（毫秒）- 极短超时强制快速响应
    "page_timeout": 3000,
    "navigation_timeout": 2000,
    "element_timeout": 1000,
    
    # 并发配置
    "max_concurrent": 3,
    "retry_attempts": 2,
    "retry_delay": 0.1,  # 100毫秒重试间隔
    
    # 浏览器优化
    "headless": True,
    "disable_images": True,
    "disable_css": True,
    "disable_fonts": True,
    "disable_extensions": True,
}

# 选择器缓存 - 预定义所有需要的选择器
SELECTORS = {
    # 场次选择相关
    "session_container": "[data-testid='session-list'], .session-list, .time-slots",
    "session_item": "[data-testid='session-item'], .session-item, .time-slot",
    "session_button": "button[data-session], .session-btn, .select-session",
    
    # 票档选择相关
    "ticket_container": "[data-testid='ticket-list'], .ticket-list, .price-list",
    "ticket_item": "[data-testid='ticket-item'], .ticket-item, .price-item",
    "ticket_button": "button[data-price], .ticket-btn, .select-ticket",
    
    # 购买按钮
    "buy_button": "[data-testid='buy-now'], .buy-now, #buyNow, button:contains('立即购买')",
    
    # 条款确认
    "agreement_checkbox": "[data-testid='agreement'], .agreement, input[type='checkbox']",
    "confirm_button": "[data-testid='confirm'], .confirm-btn, button:contains('确认订单')",
    
    # 通用选择器
    "loading": ".loading, .spinner, [data-loading]",
    "error": ".error, .alert-error, [data-error]",
}

# 浏览器启动参数 - 极速优化
BROWSER_ARGS = [
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--disable-software-rasterizer",
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding",
    "--disable-features=TranslateUI",
    "--disable-ipc-flooding-protection",
    "--disable-background-networking",
    "--disable-sync",
    "--disable-default-apps",
    "--disable-extensions",
    "--disable-plugins",
    "--disable-images",
    "--disable-javascript-harmony-shipping",
    "--memory-pressure-off",
    "--max_old_space_size=4096",
    "--aggressive-cache-discard",
    "--disable-hang-monitor",
    "--disable-prompt-on-repost",
    "--disable-client-side-phishing-detection",
    "--disable-component-update",
    "--disable-domain-reliability",
    "--disable-features=VizDisplayCompositor",
]

# 用户偏好设置
USER_PREFERENCES = {
    # 默认选择策略
    "session_preference": "earliest",  # earliest, latest, specific
    "ticket_preference": "cheapest",   # cheapest, expensive, specific
    
    # 特定选择（如果preference设为specific）
    "specific_session_index": 0,  # 第几个场次（从0开始）
    "specific_ticket_index": 0,   # 第几个票档（从0开始）
    
    # 购买数量
    "ticket_quantity": 1,
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "file": "qiangpiao.log",
}
