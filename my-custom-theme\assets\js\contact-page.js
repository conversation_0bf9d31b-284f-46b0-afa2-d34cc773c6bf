jQuery(document).ready(function($) {
    // Quick inquiry buttons functionality
    var inquiryTemplates = {
        quote: "I would like to request a quote for timing belts. Please provide pricing and specifications for:\n\n- Application: \n- Quantity: \n- Technical requirements: \n\nThank you.",
        sample: "I would like to request samples of your timing belts for evaluation. Please send samples for:\n\n- Application: \n- Belt type/size: \n- Testing requirements: \n\nPlease include technical specifications and pricing information.",
        technical: "I need technical support regarding timing belts. My question is about:\n\n- Application: \n- Current issue: \n- Technical specifications needed: \n\nPlease provide technical guidance and recommendations.",
        partnership: "I am interested in discussing a potential partnership opportunity. Our company specializes in:\n\n- Business type: \n- Market focus: \n- Partnership interests: \n\nI would like to schedule a meeting to discuss mutual opportunities."
    };

    $('.quick-btn').on('click', function() {
        // Remove active class from all buttons
        $('.quick-btn').removeClass('active');
        // Add active class to clicked button
        $(this).addClass('active');

        // Set message template
        var inquiryType = $(this).data('inquiry');
        if (inquiryTemplates[inquiryType]) {
            $('#page-message').val(inquiryTemplates[inquiryType]).focus();
        }
    });

    // File upload functionality
    var $fileUploadArea = $('.file-upload-area');
    var $fileInput = $('#page-files');

    if ($fileUploadArea.length && $fileInput.length) {
        $fileUploadArea.on('click', function() {
            $fileInput.click();
        });

        $fileUploadArea.on('dragover', function(e) {
            e.preventDefault();
            $(this).css({
                'border-color': '#2a5298',
                'background': '#f0f4ff'
            });
        });

        $fileUploadArea.on('dragleave', function(e) {
            e.preventDefault();
            $(this).css({
                'border-color': '#ddd',
                'background': '#fafafa'
            });
        });

        $fileUploadArea.on('drop', function(e) {
            e.preventDefault();
            $(this).css({
                'border-color': '#ddd',
                'background': '#fafafa'
            });

            var files = e.originalEvent.dataTransfer.files;
            $fileInput[0].files = files;
            updateFileDisplay(files);
        });

        $fileInput.on('change', function() {
            updateFileDisplay(this.files);
        });
    }

    function updateFileDisplay(files) {
        var $uploadText = $('.file-upload-text span').first();
        if (files.length > 0) {
            var fileNames = Array.from(files).map(function(file) {
                return file.name;
            }).join(', ');
            $uploadText.text('Selected: ' + fileNames);
        } else {
            $uploadText.text('Drop files here or click to upload');
        }
    }

    // Form submission handler
    $('#page-contact-form').on('submit', function(e) {
        e.preventDefault();

        // Show loading state
        var $submitBtn = $(this).find('.submit-btn');
        var $btnText = $submitBtn.find('.btn-text');
        var originalText = $btnText.text();
        $btnText.text('Sending...');
        $submitBtn.prop('disabled', true).css('opacity', '0.7');

        // Clear previous response messages
        $('#form-response').removeClass('success error').hide().html('');

        var formData = new FormData(this);
        formData.append('action', 'submit_contact_form');
        formData.append('nonce', ajax_object.nonce);

        $.ajax({
            type: 'POST',
            url: ajax_object.ajax_url,
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Show enhanced success message
                    $('#form-response').addClass('success').html(
                        '<div class="success-message">' +
                        '<div class="success-icon">✅</div>' +
                        '<h3>Thank you for your inquiry!</h3>' +
                        '<p>We have received your message and will respond within 24 hours. Our technical team will review your requirements and provide a customized solution.</p>' +
                        '</div>'
                    ).fadeIn();

                    // Reset form and buttons
                    $('#page-contact-form')[0].reset();
                    $('.quick-btn').removeClass('active');
                    updateFileDisplay([]);
                } else {
                    // Show enhanced error message
                    $('#form-response').addClass('error').html(
                        '<div class="error-message">' +
                        '<div class="error-icon">❌</div>' +
                        '<h3>Message not sent</h3>' +
                        '<p>There was an error sending your message. Please try again or contact us <NAME_EMAIL></p>' +
                        '</div>'
                    ).fadeIn();
                }

                // Restore button state
                $btnText.text(originalText);
                $submitBtn.prop('disabled', false).css('opacity', '1');

                // Scroll to response message
                $('html, body').animate({
                    scrollTop: $('#form-response').offset().top - 100
                }, 500);
            },
            error: function() {
                $('#form-response').addClass('error').html(
                    '<div class="error-message">' +
                    '<div class="error-icon">❌</div>' +
                    '<h3>Connection error</h3>' +
                    '<p>Unable to send your message due to a connection error. Please check your internet connection and try again.</p>' +
                    '</div>'
                ).fadeIn();

                $btnText.text(originalText);
                $submitBtn.prop('disabled', false).css('opacity', '1');

                $('html, body').animate({
                    scrollTop: $('#form-response').offset().top - 100
                }, 500);
            }
        });
    });
    
    // Form input field focus effects
    $('.page-contact-form input, .page-contact-form textarea, .page-contact-form select').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
    });
}); 