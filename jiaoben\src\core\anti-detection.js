/**
 * Galaxy Ticket Bot - 反检测模块
 * 混合优化方案：Fingerprint Suite + Stealth + 自定义优化
 */

import { HeaderGenerator } from 'header-generator';
import { newInjectedContext } from 'fingerprint-injector';
import UserAgent from 'user-agents';
import { config } from '../config/config.js';
import logger from '../utils/logger.js';

/**
 * 反检测管理器
 */
export class AntiDetectionManager {
  constructor() {
    this.headerGenerator = null;
    this.currentFingerprint = null;
    this.userAgentGenerator = new UserAgent({
      deviceCategory: config.antiDetection.deviceType,
      platform: this.mapOSType(config.antiDetection.osType),
    });
    
    this.initializeHeaderGenerator();
  }

  /**
   * 初始化Header生成器
   */
  initializeHeaderGenerator() {
    try {
      this.headerGenerator = new HeaderGenerator({
        browsers: config.fingerprint.suite.browsers,
        devices: config.fingerprint.suite.devices,
        operatingSystems: config.fingerprint.suite.operatingSystems,
        locales: config.fingerprint.suite.locales,
        httpVersion: config.fingerprint.suite.httpVersion,
      });
      
      logger.antiDetectionInfo('Header生成器初始化成功');
    } catch (error) {
      logger.antiDetectionInfo('Header生成器初始化失败，使用默认配置', { error: error.message });
      this.headerGenerator = new HeaderGenerator();
    }
  }

  /**
   * 映射操作系统类型
   */
  mapOSType(osType) {
    const osMap = {
      'windows': 'Win32',
      'macos': 'MacIntel',
      'linux': 'Linux x86_64',
    };
    return osMap[osType] || 'Win32';
  }

  /**
   * 生成随机指纹
   */
  generateFingerprint() {
    try {
      // 生成随机User-Agent
      const userAgent = this.userAgentGenerator.random();
      
      // 生成HTTP头
      const headers = this.headerGenerator.getHeaders({
        operatingSystems: [config.antiDetection.osType],
        devices: [config.antiDetection.deviceType],
        locales: config.fingerprint.suite.locales,
      });

      // 生成视口尺寸
      const viewport = this.generateViewport();

      // 生成时区
      const timezone = this.generateTimezone();

      const fingerprint = {
        userAgent: userAgent.toString(),
        headers,
        viewport,
        timezone,
        locale: config.fingerprint.suite.locales[0],
        platform: this.mapOSType(config.antiDetection.osType),
        deviceScaleFactor: this.generateDeviceScaleFactor(),
        colorDepth: this.generateColorDepth(),
        hardwareConcurrency: this.generateHardwareConcurrency(),
      };

      this.currentFingerprint = fingerprint;
      logger.antiDetectionInfo('新指纹生成成功', { 
        userAgent: fingerprint.userAgent.substring(0, 50) + '...',
        viewport: fingerprint.viewport,
        timezone: fingerprint.timezone,
      });

      return fingerprint;
    } catch (error) {
      logger.antiDetectionInfo('指纹生成失败，使用默认指纹', { error: error.message });
      return this.getDefaultFingerprint();
    }
  }

  /**
   * 生成视口尺寸
   */
  generateViewport() {
    const viewports = {
      desktop: [
        { width: 1920, height: 1080 },
        { width: 1366, height: 768 },
        { width: 1536, height: 864 },
        { width: 1440, height: 900 },
        { width: 1280, height: 720 },
      ],
      mobile: [
        { width: 375, height: 667 },
        { width: 414, height: 896 },
        { width: 360, height: 640 },
        { width: 390, height: 844 },
      ],
    };

    const deviceViewports = viewports[config.antiDetection.deviceType] || viewports.desktop;
    return deviceViewports[Math.floor(Math.random() * deviceViewports.length)];
  }

  /**
   * 生成时区
   */
  generateTimezone() {
    const timezones = [
      'Asia/Shanghai',
      'Asia/Hong_Kong',
      'Asia/Macau',
      'Asia/Taipei',
    ];
    return timezones[Math.floor(Math.random() * timezones.length)];
  }

  /**
   * 生成设备像素比
   */
  generateDeviceScaleFactor() {
    const factors = [1, 1.25, 1.5, 2];
    return factors[Math.floor(Math.random() * factors.length)];
  }

  /**
   * 生成颜色深度
   */
  generateColorDepth() {
    const depths = [24, 32];
    return depths[Math.floor(Math.random() * depths.length)];
  }

  /**
   * 生成硬件并发数
   */
  generateHardwareConcurrency() {
    const cores = [4, 6, 8, 12, 16];
    return cores[Math.floor(Math.random() * cores.length)];
  }

  /**
   * 获取默认指纹
   */
  getDefaultFingerprint() {
    return {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      viewport: { width: 1920, height: 1080 },
      timezone: 'Asia/Shanghai',
      locale: 'zh-CN',
      platform: 'Win32',
      deviceScaleFactor: 1,
      colorDepth: 24,
      hardwareConcurrency: 8,
    };
  }

  /**
   * 应用指纹到浏览器上下文
   */
  async applyFingerprintToContext(browser, fingerprint = null) {
    try {
      const fp = fingerprint || this.generateFingerprint();
      
      // 使用 fingerprint-injector 创建注入指纹的上下文
      let context;
      
      if (config.antiDetection.enableFingerprint) {
        context = await newInjectedContext(browser, {
          fingerprintOptions: {
            devices: [config.antiDetection.deviceType],
            operatingSystems: [config.antiDetection.osType],
          },
          newContextOptions: {
            ...config.playwright.contextOptions,
            userAgent: fp.userAgent,
            viewport: fp.viewport,
            locale: fp.locale,
            timezoneId: fp.timezone,
            deviceScaleFactor: fp.deviceScaleFactor,
            extraHTTPHeaders: fp.headers,
            // 代理配置
            ...(config.proxy.server && {
              proxy: {
                server: config.proxy.server,
                username: config.proxy.username,
                password: config.proxy.password,
              },
            }),
          },
        });
      } else {
        // 不使用指纹注入，创建普通上下文
        context = await browser.newContext({
          ...config.playwright.contextOptions,
          userAgent: fp.userAgent,
          viewport: fp.viewport,
          locale: fp.locale,
          timezoneId: fp.timezone,
          deviceScaleFactor: fp.deviceScaleFactor,
          extraHTTPHeaders: fp.headers,
          // 代理配置
          ...(config.proxy.server && {
            proxy: {
              server: config.proxy.server,
              username: config.proxy.username,
              password: config.proxy.password,
            },
          }),
        });
      }

      logger.antiDetectionInfo('指纹应用成功', {
        fingerprint: config.antiDetection.enableFingerprint,
        stealth: config.antiDetection.enableStealth,
        proxy: !!config.proxy.server,
      });

      return context;
    } catch (error) {
      logger.antiDetectionInfo('指纹应用失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 应用Stealth插件
   */
  async applyStealth(page) {
    if (!config.antiDetection.enableStealth) {
      return;
    }

    try {
      // 使用自定义stealth实现
      await this.applyCustomStealth(page);
      logger.antiDetectionInfo('Stealth插件应用成功');
    } catch (error) {
      logger.antiDetectionInfo('Stealth插件应用失败', { error: error.message });
    }
  }

  /**
   * 自定义Stealth实现
   */
  async applyCustomStealth(page) {
    await page.addInitScript(() => {
      // 隐藏webdriver属性
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });

      // 修改plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });

      // 修改languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['zh-CN', 'zh', 'en-US', 'en'],
      });
    });
  }

  /**
   * 应用自定义反检测脚本
   */
  async applyCustomAntiDetection(page) {
    try {
      // 注入自定义反检测脚本
      await page.addInitScript(() => {
        // 覆盖 webdriver 属性
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
        });

        // 覆盖 plugins 长度
        Object.defineProperty(navigator, 'plugins', {
          get: () => [1, 2, 3, 4, 5],
        });

        // 覆盖 languages
        Object.defineProperty(navigator, 'languages', {
          get: () => ['zh-CN', 'zh', 'en-US', 'en'],
        });

        // 覆盖 permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
          parameters.name === 'notifications' ?
            Promise.resolve({ state: Notification.permission }) :
            originalQuery(parameters)
        );

        // 覆盖 chrome 对象
        if (!window.chrome) {
          window.chrome = {
            runtime: {},
          };
        }

        // 覆盖 console.debug
        const originalDebug = console.debug;
        console.debug = function(...args) {
          if (args[0] && args[0].includes && args[0].includes('DevTools')) {
            return;
          }
          originalDebug.apply(console, args);
        };
      });

      logger.antiDetectionInfo('自定义反检测脚本应用成功');
    } catch (error) {
      logger.antiDetectionInfo('自定义反检测脚本应用失败', { error: error.message });
    }
  }

  /**
   * 获取当前指纹信息
   */
  getCurrentFingerprint() {
    return this.currentFingerprint;
  }

  /**
   * 重置指纹
   */
  resetFingerprint() {
    this.currentFingerprint = null;
    logger.antiDetectionInfo('指纹已重置');
  }
}

export default AntiDetectionManager;
