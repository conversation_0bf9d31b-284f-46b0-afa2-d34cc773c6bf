{"mcp": {"servers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ALLOWED_DIRECTORIES": "D:\\test\\rules,C:\\Users\\<USER>\\projects"}}}}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "typescript.preferences.includePackageJsonAutoImports": "auto", "files.associations": {"*.env.*": "dotenv"}}