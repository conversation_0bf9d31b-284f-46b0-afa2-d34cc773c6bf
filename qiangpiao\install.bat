@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   Galaxy Ticketing 极速抢票机器人
echo   一键安装脚本
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    echo 💡 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 📦 安装Python依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ 依赖安装失败，尝试使用国内镜像...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
)

echo.
echo 🎭 安装Playwright浏览器...
playwright install chromium

if errorlevel 1 (
    echo ❌ Playwright安装失败
    echo 💡 请手动运行: playwright install chromium
    pause
    exit /b 1
)

echo.
echo ✅ 安装完成！
echo.
echo 🚀 使用方法:
echo   python run.py
echo.
echo 📝 配置文件: config.py
echo 📊 日志文件: qiangpiao.log
echo.
pause
