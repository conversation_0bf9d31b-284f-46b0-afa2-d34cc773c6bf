#!/usr/bin/env python3
"""
基础测试脚本 - 检查环境和依赖
"""

import sys
import subprocess
import time

def test_python():
    """测试Python环境"""
    print("🔍 Python环境测试:")
    print(f"  版本: {sys.version}")
    print(f"  路径: {sys.executable}")
    return True

def test_pip():
    """测试pip"""
    print("\n📦 pip测试:")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True, timeout=10)
        print(f"  {result.stdout.strip()}")
        return True
    except Exception as e:
        print(f"  ❌ pip测试失败: {e}")
        return False

def install_playwright():
    """安装Playwright"""
    print("\n🎭 安装Playwright:")
    try:
        print("  正在安装playwright...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'playwright'], 
                              capture_output=True, text=True, timeout=120)
        if result.returncode == 0:
            print("  ✅ playwright安装成功")
            
            print("  正在安装浏览器...")
            result2 = subprocess.run([sys.executable, '-m', 'playwright', 'install', 'chromium'], 
                                   capture_output=True, text=True, timeout=300)
            if result2.returncode == 0:
                print("  ✅ 浏览器安装成功")
                return True
            else:
                print(f"  ❌ 浏览器安装失败: {result2.stderr}")
                return False
        else:
            print(f"  ❌ playwright安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"  ❌ 安装过程出错: {e}")
        return False

def test_playwright():
    """测试Playwright"""
    print("\n🎭 Playwright测试:")
    try:
        from playwright.sync_api import sync_playwright
        print("  ✅ Playwright导入成功")
        
        with sync_playwright() as p:
            print("  ✅ Playwright启动成功")
            browser = p.chromium.launch(headless=True)
            print("  ✅ 浏览器启动成功")
            page = browser.new_page()
            print("  ✅ 页面创建成功")
            page.goto("https://www.baidu.com", timeout=10000)
            print("  ✅ 页面导航成功")
            browser.close()
            print("  ✅ 浏览器关闭成功")
        return True
    except ImportError:
        print("  ❌ Playwright未安装")
        return False
    except Exception as e:
        print(f"  ❌ Playwright测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 Galaxy Ticketing 环境测试")
    print("=" * 50)
    
    # 测试Python
    if not test_python():
        return False
    
    # 测试pip
    if not test_pip():
        return False
    
    # 测试Playwright
    if not test_playwright():
        print("\n🔧 Playwright未安装，正在安装...")
        if install_playwright():
            print("\n🔄 重新测试Playwright...")
            if not test_playwright():
                print("❌ Playwright安装后仍然无法使用")
                return False
        else:
            print("❌ Playwright安装失败")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！环境准备就绪")
    print("💡 现在可以运行: python simple_bot.py")
    print("=" * 50)
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 环境测试失败")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        sys.exit(1)
