#!/usr/bin/env python3
"""
Galaxy Ticketing 简化版极速抢票机器人
减少依赖，专注核心功能
"""

import asyncio
import time
import sys
from pathlib import Path

# 检查并安装必要的依赖
def check_and_install_deps():
    """检查并安装必要依赖"""
    required_packages = ['playwright']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"🔧 正在安装缺失的依赖: {', '.join(missing_packages)}")
        import subprocess
        for package in missing_packages:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        print("✅ 依赖安装完成")

# 检查依赖
check_and_install_deps()

from playwright.async_api import async_playwright

class SimpleGalaxyBot:
    """简化版Galaxy抢票机器人"""
    
    def __init__(self):
        self.browser = None
        self.page = None
        self.target_url = "https://www.galaxyticketing.com/#/allEvents/detail/selectTicket?activityId=50000000740007"
        
    async def init_browser(self):
        """初始化浏览器"""
        print("🚀 启动浏览器...")
        start_time = time.time()
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器窗口便于观察
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-images',
                '--disable-javascript-harmony-shipping'
            ]
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        self.page = await context.new_page()
        
        # 禁用图片加载以提升速度
        await self.page.route("**/*.{png,jpg,jpeg,gif,svg,ico}", lambda route: route.abort())
        
        elapsed = (time.time() - start_time) * 1000
        print(f"✅ 浏览器启动完成 ({elapsed:.1f}ms)")
        
    async def navigate_to_page(self):
        """导航到目标页面"""
        print("🎯 导航到抢票页面...")
        start_time = time.time()
        
        await self.page.goto(self.target_url, wait_until='domcontentloaded', timeout=10000)
        
        elapsed = (time.time() - start_time) * 1000
        print(f"✅ 页面加载完成 ({elapsed:.1f}ms)")
        
    async def select_session(self):
        """选择场次"""
        print("⏰ 选择场次...")
        start_time = time.time()
        
        # 多种可能的场次选择器
        session_selectors = [
            '[data-testid="session-item"]',
            '.session-item',
            '.time-slot',
            'button[data-session]',
            '.session-btn'
        ]
        
        for selector in session_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=3000)
                sessions = await self.page.query_selector_all(selector)
                if sessions:
                    # 选择第一个可用场次
                    await sessions[0].click()
                    elapsed = (time.time() - start_time) * 1000
                    print(f"✅ 场次选择完成 ({elapsed:.1f}ms)")
                    return True
            except:
                continue
                
        print("❌ 未找到场次选择器")
        return False
        
    async def select_ticket(self):
        """选择票档"""
        print("🎫 选择票档...")
        start_time = time.time()
        
        # 多种可能的票档选择器
        ticket_selectors = [
            '[data-testid="ticket-item"]',
            '.ticket-item',
            '.price-item',
            'button[data-price]',
            '.ticket-btn'
        ]
        
        for selector in ticket_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=3000)
                tickets = await self.page.query_selector_all(selector)
                if tickets:
                    # 选择第一个可用票档（通常是最便宜的）
                    await tickets[0].click()
                    elapsed = (time.time() - start_time) * 1000
                    print(f"✅ 票档选择完成 ({elapsed:.1f}ms)")
                    return True
            except:
                continue
                
        print("❌ 未找到票档选择器")
        return False
        
    async def click_buy_now(self):
        """点击立即购买"""
        print("💰 点击立即购买...")
        start_time = time.time()
        
        # 多种可能的购买按钮选择器
        buy_selectors = [
            '[data-testid="buy-now"]',
            '.buy-now',
            '#buyNow',
            'button:has-text("立即购买")',
            'button:has-text("购买")',
            '.purchase-btn'
        ]
        
        for selector in buy_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=3000)
                await self.page.click(selector)
                elapsed = (time.time() - start_time) * 1000
                print(f"✅ 购买按钮点击完成 ({elapsed:.1f}ms)")
                return True
            except:
                continue
                
        print("❌ 未找到购买按钮")
        return False
        
    async def confirm_order(self):
        """确认订单"""
        print("📋 确认订单...")
        start_time = time.time()
        
        # 先尝试勾选同意条款
        agreement_selectors = [
            '[data-testid="agreement"]',
            '.agreement',
            'input[type="checkbox"]',
            '.terms-checkbox'
        ]
        
        for selector in agreement_selectors:
            try:
                checkbox = await self.page.query_selector(selector)
                if checkbox:
                    await checkbox.click()
                    break
            except:
                continue
        
        # 点击确认按钮
        confirm_selectors = [
            '[data-testid="confirm"]',
            '.confirm-btn',
            'button:has-text("确认订单")',
            'button:has-text("确认")',
            '.order-confirm'
        ]
        
        for selector in confirm_selectors:
            try:
                await self.page.wait_for_selector(selector, timeout=3000)
                await self.page.click(selector)
                elapsed = (time.time() - start_time) * 1000
                print(f"✅ 订单确认完成 ({elapsed:.1f}ms)")
                return True
            except:
                continue
                
        print("❌ 未找到确认按钮")
        return False
        
    async def run(self):
        """运行抢票流程"""
        total_start = time.time()
        
        print("=" * 50)
        print("🎉 Galaxy Ticketing 极速抢票机器人启动")
        print("=" * 50)
        
        try:
            # 执行抢票流程
            steps = [
                ("初始化浏览器", self.init_browser),
                ("导航到页面", self.navigate_to_page),
                ("选择场次", self.select_session),
                ("选择票档", self.select_ticket),
                ("点击购买", self.click_buy_now),
                ("确认订单", self.confirm_order)
            ]
            
            for step_name, step_func in steps:
                success = await step_func()
                if not success:
                    print(f"❌ {step_name}失败，停止执行")
                    return
                    
                # 短暂等待，确保页面响应
                await asyncio.sleep(0.2)
            
            total_time = (time.time() - total_start) * 1000
            print("\n" + "=" * 50)
            print("🎉 抢票流程完成！")
            print(f"⚡ 总用时: {total_time:.1f}ms")
            
            if total_time < 5000:
                print("🏆 极速完成！")
            elif total_time < 10000:
                print("🚀 快速完成！")
            else:
                print("✅ 任务完成")
                
            print("💡 现在可以手动完成支付流程")
            print("=" * 50)
            
            # 保持浏览器打开，让用户完成支付
            print("\n⏳ 浏览器将保持打开状态，您可以继续完成支付...")
            print("按 Ctrl+C 退出程序")
            
            # 等待用户操作
            while True:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⏹️  用户停止程序")
        except Exception as e:
            print(f"\n❌ 程序出错: {e}")
        finally:
            if self.browser:
                await self.browser.close()
                print("🔚 浏览器已关闭")

async def main():
    """主函数"""
    bot = SimpleGalaxyBot()
    await bot.run()

if __name__ == "__main__":
    print("🔍 检查Python版本...")
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} 检查通过")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 请确保已安装playwright: pip install playwright")
        print("💡 然后运行: playwright install chromium")
