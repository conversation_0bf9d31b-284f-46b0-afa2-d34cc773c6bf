<execution>
  <constraint>
    ## 技术约束条件
    - **性能要求**：系统响应时间 < 2秒，支持1000+并发用户
    - **可用性要求**：99.9%系统可用性，7x24小时稳定运行
    - **安全要求**：数据加密传输，权限控制，审计日志
    - **合规要求**：GDPR、CCPA等数据保护法规遵循
    - **扩展性要求**：支持水平扩展，模块化部署
    
    ## 业务约束条件
    - **多语言支持**：至少支持中英文，预留其他语言扩展
    - **多货币支持**：美元、欧元、人民币等主要货币
    - **时区处理**：全球时区的正确处理和显示
    - **移动端适配**：响应式设计，移动端用户体验优化
  </constraint>
  
  <rule>
    ## 开发规范
    - **代码规范**：统一的编码标准和代码审查流程
    - **API设计**：RESTful API设计，统一的接口规范
    - **数据库设计**：规范化设计，索引优化，备份策略
    - **测试规范**：单元测试覆盖率 > 80%，集成测试完整
    - **文档规范**：API文档、部署文档、用户手册完整
    
    ## 安全规范
    - **身份认证**：多因素认证，单点登录支持
    - **权限控制**：基于角色的访问控制（RBAC）
    - **数据保护**：敏感数据加密存储和传输
    - **审计日志**：完整的操作日志记录和分析
  </rule>
  
  <guideline>
    ## 架构设计指导
    - **微服务架构**：按业务域拆分服务，独立部署和扩展
    - **API网关**：统一的API入口，流量控制和安全认证
    - **消息队列**：异步处理，系统解耦，提升性能
    - **缓存策略**：多级缓存，减少数据库压力
    - **监控告警**：全链路监控，智能告警机制
    
    ## 数据库设计指导
    - **主从分离**：读写分离，提升数据库性能
    - **分库分表**：大数据量的水平拆分策略
    - **索引优化**：合理的索引设计，查询性能优化
    - **备份恢复**：定期备份，灾难恢复方案
    
    ## 前端开发指导
    - **组件化开发**：可复用的UI组件库
    - **状态管理**：统一的状态管理方案
    - **性能优化**：代码分割，懒加载，CDN加速
    - **用户体验**：响应式设计，交互优化
  </guideline>
  
  <process>
    ## 系统开发流程
    
    ### Phase 1: 需求分析与设计 (2-3周)
    ```mermaid
    flowchart TD
        A[业务需求调研] --> B[功能需求分析]
        B --> C[技术方案设计]
        C --> D[数据库设计]
        D --> E[API接口设计]
        E --> F[UI/UX设计]
        F --> G[开发计划制定]
    ```
    
    ### Phase 2: 核心功能开发 (6-8周)
    ```mermaid
    graph LR
        A[客户管理模块] --> B[产品管理模块]
        B --> C[询盘处理模块]
        C --> D[订单管理模块]
        D --> E[基础报表模块]
    ```
    
    ### Phase 3: 营销功能集成 (4-6周)
    ```mermaid
    flowchart TD
        A[Facebook API集成] --> B[多渠道营销管理]
        B --> C[营销自动化]
        C --> D[效果分析报表]
        D --> E[客户行为分析]
    ```
    
    ### Phase 4: 系统集成与测试 (3-4周)
    ```mermaid
    graph TD
        A[第三方系统集成] --> B[系统集成测试]
        B --> C[性能压力测试]
        C --> D[安全渗透测试]
        D --> E[用户验收测试]
    ```
    
    ### Phase 5: 部署与上线 (1-2周)
    ```mermaid
    flowchart LR
        A[生产环境部署] --> B[数据迁移]
        B --> C[系统监控配置]
        C --> D[用户培训]
        D --> E[正式上线]
    ```
    
    ## 持续集成/持续部署流程
    
    ```mermaid
    flowchart TD
        A[代码提交] --> B[自动化测试]
        B --> C{测试通过?}
        C -->|是| D[构建镜像]
        C -->|否| E[通知开发者]
        D --> F[部署到测试环境]
        F --> G[集成测试]
        G --> H{测试通过?}
        H -->|是| I[部署到生产环境]
        H -->|否| E
        I --> J[监控告警]
    ```
  </process>
  
  <criteria>
    ## 质量评价标准

    ### 功能完整性
    - ✅ 核心业务流程完整覆盖
    - ✅ 用户角色权限管理完善
    - ✅ 数据导入导出功能完整
    - ✅ 报表分析功能满足需求

    ### 性能指标
    - ✅ 页面加载时间 < 2秒
    - ✅ API响应时间 < 500ms
    - ✅ 并发用户数 > 1000
    - ✅ 系统可用性 > 99.9%

    ### 安全合规
    - ✅ 数据加密传输和存储
    - ✅ 权限控制粒度合理
    - ✅ 审计日志完整可追溯
    - ✅ 符合GDPR等法规要求

    ### 用户体验
    - ✅ 界面设计简洁直观
    - ✅ 操作流程符合用户习惯
    - ✅ 移动端体验良好
    - ✅ 多语言支持完善

    ### 可维护性
    - ✅ 代码结构清晰，注释完整
    - ✅ 文档齐全，便于维护
    - ✅ 模块化设计，易于扩展
    - ✅ 监控告警机制完善
  </criteria>
</execution>
