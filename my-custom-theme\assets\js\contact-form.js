// Contact Box and Form Functionality
jQuery(document).ready(function($) {
    // Variables
    const contactTrigger = $('.contact-box-trigger');
    const contactModal = $('.contact-modal');
    const contactFormClose = $('.contact-form-close');
    const contactForm = $('#contact-form');
    
    // Open contact form modal
    contactTrigger.on('click', function() {
        contactModal.addClass('active');
        $('body').addClass('modal-open');
    });
    
    // Close contact form modal
    contactFormClose.on('click', function() {
        contactModal.removeClass('active');
        $('body').removeClass('modal-open');
    });
    
    // Close modal when clicking outside the form
    $(document).on('click', function(e) {
        if (contactModal.hasClass('active') && 
            !$(e.target).closest('.contact-form-container').length && 
            !$(e.target).closest('.contact-box-trigger').length) {
            contactModal.removeClass('active');
            $('body').removeClass('modal-open');
        }
    });
    
    // Handle form submission
    contactForm.on('submit', function(e) {
        e.preventDefault();
        
        const formData = $(this).serialize();
        const submitButton = $(this).find('button[type="submit"]');
        const formResponse = $('<div class="form-response"></div>');
        
        // Remove any existing response message
        $('.form-response').remove();
        
        // Disable submit button and show loading state
        submitButton.prop('disabled', true).text('Submitting...');
        
        // Send AJAX request
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'submit_contact_form',
                form_data: formData,
                nonce: ajax_object.ajax_nonce
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    formResponse.html(response.data.message).addClass('success');
                    contactForm.hide();
                    $('.contact-form-container').append(formResponse);
                    
                    // Reset form after 3 seconds
                    setTimeout(function() {
                        contactForm[0].reset();
                    }, 3000);
                } else {
                    // Show error message
                    formResponse.html(response.data.message).addClass('error');
                    contactForm.after(formResponse);
                    submitButton.prop('disabled', false).text('Submit');
                }
            },
            error: function() {
                // Show general error message
                formResponse.html('An error occurred. Please try again later.').addClass('error');
                contactForm.after(formResponse);
                submitButton.prop('disabled', false).text('Submit');
            }
        });
    });
    
    // Form validation
    const validateForm = function() {
        let isValid = true;
        const requiredFields = contactForm.find('[required]');
        
        // Reset previous validation
        $('.error-message').remove();
        $('input, select, textarea').removeClass('error');
        
        // Check each required field
        requiredFields.each(function() {
            const field = $(this);
            
            if (field.val().trim() === '') {
                isValid = false;
                field.addClass('error');
                
                // Add error message
                const errorMessage = $('<span class="error-message">This field is required</span>');
                field.after(errorMessage);
            }
            
            // Special validation for email
            if (field.attr('type') === 'email' && field.val().trim() !== '') {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(field.val().trim())) {
                    isValid = false;
                    field.addClass('error');
                    
                    // Add error message
                    const errorMessage = $('<span class="error-message">Please enter a valid email address</span>');
                    field.after(errorMessage);
                }
            }
        });
        
        return isValid;
    };
    
    // Validate on submit
    contactForm.on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
    
    // Clear errors when field is focused
    contactForm.find('input, select, textarea').on('focus', function() {
        $(this).removeClass('error');
        $(this).next('.error-message').remove();
    });
}); 