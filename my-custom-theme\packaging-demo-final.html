<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Packaging Solutions - Fixed Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 40px 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
            font-weight: 600;
        }

        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1rem;
        }

        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .solution-card {
            background: white;
            border-radius: 12px;
            padding: 0;
            height: 400px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .card-header {
            text-align: center;
            margin-bottom: 0;
            padding: 25px 25px 20px 25px;
            border-radius: 12px 12px 0 0;
        }

        .solution-card:nth-child(1) .card-header { background: #ff6b35; }
        .solution-card:nth-child(2) .card-header { background: #4caf50; }
        .solution-card:nth-child(3) .card-header { background: #2196f3; }
        .solution-card:nth-child(4) .card-header { background: #9c27b0; }

        .card-content {
            padding: 20px 25px 25px 25px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .card-title {
            color: white;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .card-subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 0.95rem;
            font-weight: 500;
        }

        .features-list {
            flex: 1;
            margin-bottom: 15px;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            color: white;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            font-size: 0.9rem;
        }

        .features-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: rgba(255,255,255,0.9);
            font-weight: bold;
        }

        .applications-section {
            margin-top: auto;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .applications-title {
            color: white;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .applications-content {
            color: rgba(255,255,255,0.9);
            font-size: 0.85rem;
            line-height: 1.4;
            height: 60px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        /* Hidden features that appear on hover */
        .hidden-features {
            opacity: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            margin-top: 0;
        }

        .hidden-features ul {
            list-style: none;
            padding: 0;
        }

        .hidden-features li {
            color: white;
            margin-bottom: 6px;
            padding-left: 20px;
            position: relative;
            font-size: 0.9rem;
        }

        .hidden-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: rgba(255,255,255,0.9);
            font-weight: bold;
        }

        /* Hover effects */
        .solution-card:hover .hidden-features {
            opacity: 1;
            max-height: 200px;
            margin-top: 10px;
        }

        .solution-card:hover .applications-content {
            height: 40px;
            font-size: 0.8rem;
        }

        .solution-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Packaging Solutions</h1>
        <p class="subtitle">Hover over cards to see complete features list - Applications section will compress to make space</p>
        
        <div class="solutions-grid">
            <div class="solution-card">
                <div class="card-header">
                    <h3 class="card-title">High-Speed Timing Belts</h3>
                    <p class="card-subtitle">Up to 50 m/s</p>
                </div>

                <div class="card-content">
                    <div class="features-list">
                        <ul>
                            <li>Low stretch polyurethane construction</li>
                            <li>Precision-ground tooth profile</li>
                            <li>Dynamic balancing for smooth operation</li>
                        </ul>
                    </div>

                    <div class="hidden-features">
                        <ul>
                            <li>Reduced vibration and noise</li>
                            <li>Extended fatigue life</li>
                            <li>Temperature resistant compounds</li>
                            <li>Custom lengths available</li>
                        </ul>
                    </div>

                    <div class="applications-section">
                        <h4 class="applications-title">Applications:</h4>
                        <p class="applications-content">High-speed packaging machinery, automated sorting systems, conveyor belt applications requiring precise timing and minimal maintenance.</p>
                    </div>
                </div>
            </div>

            <div class="solution-card">
                <div class="card-header">
                    <h3 class="card-title">Food-Grade Belts</h3>
                    <p class="card-subtitle">FDA Compliant</p>
                </div>

                <div class="card-content">
                    <div class="features-list">
                        <ul>
                            <li>FDA 21 CFR 177.2600 approved materials</li>
                            <li>Smooth, non-porous surface</li>
                            <li>Resistant to oils and cleaning agents</li>
                        </ul>
                    </div>

                    <div class="hidden-features">
                        <ul>
                            <li>Easy to clean and sanitize</li>
                            <li>No bacterial growth</li>
                            <li>Temperature range -40°C to +80°C</li>
                            <li>HACCP compliant</li>
                        </ul>
                    </div>

                    <div class="applications-section">
                        <h4 class="applications-title">Applications:</h4>
                        <p class="applications-content">Food processing lines, beverage packaging, pharmaceutical manufacturing, and any application requiring strict hygiene standards.</p>
                    </div>
                </div>
            </div>

            <div class="solution-card">
                <div class="card-header">
                    <h3 class="card-title">Precision Positioning Belts</h3>
                    <p class="card-subtitle">±0.05mm Accuracy</p>
                </div>

                <div class="card-content">
                    <div class="features-list">
                        <ul>
                            <li>Ultra-precise tooth geometry</li>
                            <li>Minimal backlash design</li>
                            <li>Superior dimensional stability</li>
                        </ul>
                    </div>

                    <div class="hidden-features">
                        <ul>
                            <li>Steel cord reinforcement</li>
                            <li>Pre-tensioned construction</li>
                            <li>Low elongation under load</li>
                            <li>Consistent pitch accuracy</li>
                        </ul>
                    </div>

                    <div class="applications-section">
                        <h4 class="applications-title">Applications:</h4>
                        <p class="applications-content">Precision labeling machines, pick-and-place systems, robotic packaging cells, and high-accuracy positioning applications.</p>
                    </div>
                </div>
            </div>

            <div class="solution-card">
                <div class="card-header">
                    <h3 class="card-title">Cleanroom Belts</h3>
                    <p class="card-subtitle">ISO Class 5</p>
                </div>

                <div class="card-content">
                    <div class="features-list">
                        <ul>
                            <li>Low particle generation materials</li>
                            <li>Antistatic properties available</li>
                            <li>Chemical resistant compounds</li>
                        </ul>
                    </div>

                    <div class="hidden-features">
                        <ul>
                            <li>Validated for cleanroom use</li>
                            <li>Smooth surface finish</li>
                            <li>No outgassing materials</li>
                            <li>ESD safe options</li>
                        </ul>
                    </div>

                    <div class="applications-section">
                        <h4 class="applications-title">Applications:</h4>
                        <p class="applications-content">Semiconductor packaging, medical device assembly, pharmaceutical cleanrooms, and electronics manufacturing environments.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
