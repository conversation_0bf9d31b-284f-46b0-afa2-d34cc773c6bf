{"globalShortcut": "Cmd+Shift+Space", "mcpServers": {"memory-keeper": {"command": "npx", "args": ["-y", "mcp-memory-keeper@latest"], "env": {"MEMORY_DIR": "C:\\Users\\<USER>\\claude-memory"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ALLOWED_DIRECTORIES": "D:\\test\\rules,C:\\Users\\<USER>\\projects,C:\\Users\\<USER>\\Documents,C:\\Users\\<USER>\\claude-memory"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"]}}, "customInstructions": "Follow the rules and best practices in the INTELLIGENT_ASSISTANT_RULES.md file. I am an intelligent development assistant with AURA-X protocol (寸止+Context7-mcp+记忆+Playwright+Sequential thinking) capabilities."}