/* Contact box style for bottom right corner - 只针对固定位置的联系框 */
.contact-box-fixed {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: auto;
  min-width: 140px;
  background-color: #ff6a00; /* Orange color, matching the Contact Us button in the header */
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.contact-box-fixed:hover {
  background-color: #e65c00; /* Darker orange on hover */
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.contact-box-fixed span {
  font-weight: 600;
  font-size: 16px;
  white-space: nowrap;
}

.contact-box-fixed i {
  font-size: 18px;
  opacity: 0.9;
}

/* 确保没有额外的伪元素内容 */
.contact-box-fixed::before,
.contact-box-fixed::after {
  display: none !important;
}

/* 移动端响应式调整 */
@media (max-width: 768px) {
  .contact-box-fixed {
    bottom: 20px;
    right: 20px;
    padding: 12px 16px;
    min-width: 120px;
  }

  .contact-box-fixed span {
    font-size: 14px;
  }

  .contact-box-fixed i {
    font-size: 16px;
  }
}

/* Prevent page scrolling when modal is open */
body.modal-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

/* Contact form modal styles - enhanced version */
.contact-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: none;
  justify-content: center;
  align-items: flex-start; /* Align to top */
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  overflow-y: auto; /* Add vertical scrollbar */
  padding: 30px 0; /* Add top and bottom padding */
}

.contact-modal.active {
  display: flex;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.contact-form-container {
  width: 90%;
  max-width: 600px;
  background-color: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
  position: relative;
  animation: slideUp 0.4s ease;
  background-image: linear-gradient(to bottom, #ffffff, #f9f9f9);
  border: 1px solid rgba(255, 255, 255, 0.8);
  margin: 20px auto; /* Add top and bottom margin */
  max-height: 90vh; /* Limit maximum height */
  overflow-y: auto; /* Add internal scrollbar */
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.contact-form-container h2 {
  margin-top: 0;
  color: #333;
  font-size: 28px;
  margin-bottom: 30px;
  text-align: center;
  font-weight: 600;
  position: relative;
  padding-bottom: 15px;
}

.contact-form-container h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #ff6a00;
  border-radius: 2px;
}

.contact-form-close {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 28px;
  cursor: pointer;
  color: #999;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  z-index: 10; /* Ensure close button is on top */
}

.contact-form-close:hover {
  color: #ff6a00;
  background-color: #f5f5f5;
}

.contact-form {
  position: relative;
  padding-right: 5px; /* Leave space for scrollbar */
}

.contact-form .form-group {
  margin-bottom: 24px;
}

.contact-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #444;
  font-size: 15px;
}

.contact-form input,
.contact-form select,
.contact-form textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: #ff6a00;
  box-shadow: 0 0 0 3px rgba(255, 106, 0, 0.15);
  background-color: #fff;
}

.contact-form textarea {
  height: 140px;
  resize: vertical;
}

.contact-form .required {
  color: #ff6a00; /* Orange color, matching button color */
  margin-left: 3px;
}

/* Two-column layout */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.form-col {
  flex: 1 0 50%;
  padding: 0 10px;
}

@media (max-width: 768px) {
  .form-col {
    flex: 1 0 100%;
  }
}

.contact-form button {
  background-color: #ff6a00; /* Orange color, matching button color */
  color: white;
  border: none;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  margin: 30px auto 0;
  min-width: 180px;
  box-shadow: 0 4px 10px rgba(255, 106, 0, 0.2);
}

.contact-form button:hover {
  background-color: #e65c00; /* Darker orange on hover */
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 106, 0, 0.25);
}

.contact-form button:active {
  transform: translateY(0);
}

.gdpr-checkbox {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #eee;
}

.gdpr-checkbox input {
  width: auto;
  margin-right: 12px;
  margin-top: 5px;
  transform: scale(1.2);
}

.gdpr-checkbox label {
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}

/* Success message styles */
.success-message {
  text-align: center;
  padding: 20px 0;
}

.success-message p {
  font-size: 18px;
  color: #2e7d32;
  margin-top: 15px;
}

.success-icon {
  display: inline-block;
  width: 80px;
  height: 80px;
  position: relative;
  border-radius: 50%;
  border: 4px solid #4CAF50;
  margin-bottom: 20px;
}

.success-icon:before,
.success-icon:after {
  content: '';
  position: absolute;
  background-color: #4CAF50;
}

.success-icon:before {
  width: 4px;
  height: 30px;
  left: 28px;
  top: 35px;
  transform: rotate(-45deg);
}

.success-icon:after {
  width: 4px;
  height: 60px;
  left: 46px;
  top: 13px;
  transform: rotate(45deg);
}

/* Custom scrollbar for form container */
.contact-form-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.contact-form-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.contact-form-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 10px;
}

.contact-form-container::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 联系表单容器响应式调整 */
@media (max-width: 768px) {
  .contact-form-container {
    padding: 30px 20px;
    width: 95%;
  }

  .contact-form-container h2 {
    font-size: 24px;
  }

  .contact-modal {
    padding: 15px 0;
  }
}

@media (max-height: 700px) {
  .contact-form-container {
    padding: 25px 20px;
    max-height: 95vh;
  }
  
  .contact-form-container h2 {
    font-size: 22px;
    margin-bottom: 20px;
    padding-bottom: 10px;
  }
  
  .contact-form .form-group {
    margin-bottom: 15px;
  }
} 