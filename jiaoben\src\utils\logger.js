/**
 * Galaxy Ticket Bot - 日志管理模块
 * 提供结构化日志记录功能
 */

import winston from 'winston';
import chalk from 'chalk';
import path from 'path';
import { fileURLToPath } from 'url';
import { config } from '../config/config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 自定义日志格式
const customFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// 控制台格式
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const colorMap = {
      error: chalk.red,
      warn: chalk.yellow,
      info: chalk.blue,
      debug: chalk.gray,
      success: chalk.green,
    };

    const colorFn = colorMap[level] || chalk.white;
    const prefix = colorFn(`[${timestamp}] ${level.toUpperCase()}`);
    
    let output = `${prefix} ${message}`;
    
    // 添加元数据
    if (Object.keys(meta).length > 0) {
      output += chalk.gray(` ${JSON.stringify(meta)}`);
    }
    
    return output;
  })
);

// 创建日志器
const logger = winston.createLogger({
  level: config.advanced.logLevel,
  format: customFormat,
  transports: [
    // 文件日志
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// 开发环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
  }));
}

/**
 * 扩展日志器功能
 */
class ExtendedLogger {
  constructor(baseLogger) {
    this.logger = baseLogger;
  }

  // 基础日志方法
  error(message, meta = {}) {
    this.logger.error(message, meta);
  }

  warn(message, meta = {}) {
    this.logger.warn(message, meta);
  }

  info(message, meta = {}) {
    this.logger.info(message, meta);
  }

  debug(message, meta = {}) {
    this.logger.debug(message, meta);
  }

  // 自定义成功日志
  success(message, meta = {}) {
    this.logger.log('success', message, meta);
  }

  // 购票相关日志
  ticketInfo(message, meta = {}) {
    this.info(`🎫 ${message}`, { category: 'ticket', ...meta });
  }

  ticketSuccess(message, meta = {}) {
    this.success(`🎉 ${message}`, { category: 'ticket', ...meta });
  }

  ticketError(message, meta = {}) {
    this.error(`❌ ${message}`, { category: 'ticket', ...meta });
  }

  // 反检测相关日志
  antiDetectionInfo(message, meta = {}) {
    this.info(`🛡️ ${message}`, { category: 'anti-detection', ...meta });
  }

  // 网络相关日志
  networkInfo(message, meta = {}) {
    this.info(`🌐 ${message}`, { category: 'network', ...meta });
  }

  networkError(message, meta = {}) {
    this.error(`🔌 ${message}`, { category: 'network', ...meta });
  }

  // 浏览器相关日志
  browserInfo(message, meta = {}) {
    this.info(`🌍 ${message}`, { category: 'browser', ...meta });
  }

  browserError(message, meta = {}) {
    this.error(`💥 ${message}`, { category: 'browser', ...meta });
  }

  // 性能监控日志
  performance(action, duration, meta = {}) {
    this.info(`⏱️ ${action} 耗时: ${duration}ms`, { 
      category: 'performance', 
      action,
      duration,
      ...meta 
    });
  }

  // 步骤日志
  step(stepNumber, totalSteps, message, meta = {}) {
    this.info(`📋 [${stepNumber}/${totalSteps}] ${message}`, {
      category: 'step',
      stepNumber,
      totalSteps,
      ...meta
    });
  }

  // 配置日志
  config(message, meta = {}) {
    this.info(`⚙️ ${message}`, { category: 'config', ...meta });
  }

  // 安全相关日志
  security(message, meta = {}) {
    this.warn(`🔒 ${message}`, { category: 'security', ...meta });
  }

  // 用户操作日志
  userAction(action, meta = {}) {
    this.info(`👤 用户操作: ${action}`, { 
      category: 'user-action', 
      action,
      ...meta 
    });
  }

  // 系统状态日志
  systemStatus(status, meta = {}) {
    const emoji = status === 'healthy' ? '✅' : status === 'warning' ? '⚠️' : '❌';
    this.info(`${emoji} 系统状态: ${status}`, { 
      category: 'system', 
      status,
      ...meta 
    });
  }

  // 创建子日志器
  child(defaultMeta = {}) {
    const childLogger = this.logger.child(defaultMeta);
    return new ExtendedLogger(childLogger);
  }

  // 日志统计
  getStats() {
    // 这里可以实现日志统计功能
    return {
      totalLogs: 0,
      errorCount: 0,
      warnCount: 0,
      infoCount: 0,
    };
  }
}

// 创建扩展日志器实例
const extendedLogger = new ExtendedLogger(logger);

// 导出日志器
export default extendedLogger;

// 导出日志级别常量
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
  SUCCESS: 'success',
};

// 导出便捷方法
export const log = extendedLogger;
