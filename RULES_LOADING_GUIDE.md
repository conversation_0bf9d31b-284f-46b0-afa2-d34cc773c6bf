# 🚀 AI助手规则文件加载指南

## 📋 支持的平台

### 1. <PERSON> Code (命令行)

#### 方法一：全局规则文件
```bash
# 创建Claude配置目录
mkdir -p ~/.claude

# 复制规则文件到全局配置
cp INTELLIGENT_ASSISTANT_RULES.md ~/.claude/CLAUDE.md

# 验证配置
claude --help
```

#### 方法二：项目级规则
```bash
# 在项目根目录创建规则文件
cp INTELLIGENT_ASSISTANT_RULES.md ./CLAUDE.md

# 或者创建 .clinerules 文件
cp INTELLIGENT_ASSISTANT_RULES.md ./.clinerules
```

### 2. Cursor IDE

#### 方法一：工作区规则
```bash
# 在项目根目录创建 .cursorrules 文件
cp INTELLIGENT_ASSISTANT_RULES.md ./.cursorrules
```

#### 方法二：全局规则
```bash
# macOS/Linux
cp INTELLIGENT_ASSISTANT_RULES.md ~/.cursor/rules.md

# Windows
copy INTELLIGENT_ASSISTANT_RULES.md %APPDATA%\Cursor\rules.md
```

#### 方法三：通过Cursor设置
1. 打开 Cursor
2. 按 `Cmd/Ctrl + ,` 打开设置
3. 搜索 "rules" 或 "custom instructions"
4. 将规则文件内容粘贴到相应字段

### 3. VS Code + AI扩展

#### GitHub Copilot
```json
// settings.json
{
  "github.copilot.enable": {
    "*": true
  },
  "github.copilot.advanced": {
    "debug.overrideEngine": "codex",
    "debug.testOverrideProxyUrl": "",
    "debug.overrideProxyUrl": "",
    "length": 500
  }
}
```

#### Continue.dev 扩展
```json
// .continue/config.json
{
  "models": [
    {
      "title": "Claude 3.5 Sonnet",
      "provider": "anthropic",
      "model": "claude-3-5-sonnet-20241022",
      "systemMessage": "请加载 INTELLIGENT_ASSISTANT_RULES.md 中的规则"
    }
  ],
  "customCommands": [
    {
      "name": "load-rules",
      "prompt": "请根据项目根目录的 INTELLIGENT_ASSISTANT_RULES.md 文件中的规则来协助我"
    }
  ]
}
```

### 4. Claude Desktop

#### 配置文件位置
```bash
# macOS
~/Library/Application Support/Claude/claude_desktop_config.json

# Windows
%APPDATA%\Claude\claude_desktop_config.json
```

#### 配置示例
```json
{
  "globalShortcut": "Cmd+Shift+Space",
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem"],
      "env": {
        "ALLOWED_DIRECTORIES": "/path/to/your/project"
      }
    }
  },
  "customInstructions": "请遵循项目中 INTELLIGENT_ASSISTANT_RULES.md 文件的规则和指导原则"
}
```

### 5. 其他AI助手平台

#### ChatGPT
1. 打开 ChatGPT
2. 点击用户头像 → Settings → Personalization
3. 在 "Custom instructions" 中添加：
```
请遵循以下规则文件的指导原则：[粘贴 INTELLIGENT_ASSISTANT_RULES.md 的关键内容]
```

#### Windsurf
```bash
# 在项目根目录创建
cp INTELLIGENT_ASSISTANT_RULES.md ./.windsurfrules
```

#### Codeium
```json
// .codeium/config.json
{
  "customInstructions": "请遵循 INTELLIGENT_ASSISTANT_RULES.md 中的开发规则和最佳实践"
}
```

## 🔧 自动化加载脚本

### 通用加载脚本
```bash
#!/bin/bash
# load-ai-rules.sh

RULES_FILE="INTELLIGENT_ASSISTANT_RULES.md"

# 检查规则文件是否存在
if [ ! -f "$RULES_FILE" ]; then
    echo "❌ 规则文件 $RULES_FILE 不存在"
    exit 1
fi

echo "🚀 开始加载AI助手规则..."

# Claude Code
if command -v claude &> /dev/null; then
    mkdir -p ~/.claude
    cp "$RULES_FILE" ~/.claude/CLAUDE.md
    echo "✅ Claude Code 规则已加载"
fi

# Cursor
if [ -d "/Applications/Cursor.app" ] || command -v cursor &> /dev/null; then
    cp "$RULES_FILE" ./.cursorrules
    echo "✅ Cursor 规则已加载"
fi

# VS Code Continue
if [ -d ".continue" ]; then
    mkdir -p .continue
    echo "✅ Continue.dev 配置目录已准备"
fi

echo "🎉 规则加载完成！"
```

### Windows PowerShell 脚本
```powershell
# load-ai-rules.ps1

$RulesFile = "INTELLIGENT_ASSISTANT_RULES.md"

if (-not (Test-Path $RulesFile)) {
    Write-Host "❌ 规则文件 $RulesFile 不存在" -ForegroundColor Red
    exit 1
}

Write-Host "🚀 开始加载AI助手规则..." -ForegroundColor Green

# Claude Code
$ClaudeDir = "$env:USERPROFILE\.claude"
if (-not (Test-Path $ClaudeDir)) {
    New-Item -ItemType Directory -Path $ClaudeDir -Force
}
Copy-Item $RulesFile "$ClaudeDir\CLAUDE.md"
Write-Host "✅ Claude Code 规则已加载" -ForegroundColor Green

# Cursor
Copy-Item $RulesFile ".cursorrules"
Write-Host "✅ Cursor 规则已加载" -ForegroundColor Green

# Claude Desktop
$ClaudeDesktopConfig = "$env:APPDATA\Claude\claude_desktop_config.json"
if (Test-Path $ClaudeDesktopConfig) {
    Write-Host "✅ 发现 Claude Desktop 配置文件" -ForegroundColor Green
}

Write-Host "🎉 规则加载完成！" -ForegroundColor Green
```

## ✅ 验证加载成功

### 测试命令
```bash
# 测试AI助手是否识别规则
echo "请告诉我你当前遵循的开发规则和最佳实践"

# 测试终端自动化
echo "请帮我检查当前项目的健康状态"

# 测试MCP集成
echo "请列出可用的MCP服务器"
```

### 预期响应
AI助手应该能够：
1. 引用规则文件中的具体内容
2. 主动执行安全的只读操作
3. 提供基于最佳实践的建议
4. 展示MCP服务器集成能力

## 🔄 规则更新

### 自动更新脚本
```bash
#!/bin/bash
# update-rules.sh

REPO_URL="https://raw.githubusercontent.com/your-repo/ai-rules/main/INTELLIGENT_ASSISTANT_RULES.md"
LOCAL_RULES="INTELLIGENT_ASSISTANT_RULES.md"

# 备份当前规则
if [ -f "$LOCAL_RULES" ]; then
    cp "$LOCAL_RULES" "${LOCAL_RULES}.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 下载最新规则
curl -s "$REPO_URL" -o "$LOCAL_RULES"

# 重新加载到各个平台
./load-ai-rules.sh

echo "🎉 规则已更新并重新加载！"
```

## 📞 故障排除

### 常见问题
1. **规则未生效**
   - 检查文件路径是否正确
   - 重启AI助手应用
   - 验证文件权限

2. **MCP服务器连接失败**
   - 检查网络连接
   - 验证API密钥配置
   - 查看错误日志

3. **配置文件格式错误**
   - 验证JSON格式
   - 检查环境变量设置
   - 使用配置验证工具

### 获取帮助
- 查看AI助手官方文档
- 检查项目GitHub Issues
- 联系技术支持团队

---

**选择适合您的平台，按照相应步骤加载规则文件即可！** 🚀
