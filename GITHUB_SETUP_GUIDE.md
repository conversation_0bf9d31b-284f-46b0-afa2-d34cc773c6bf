# 🐙 GitHub配置完整指南

> **用户**: sanmu  
> **邮箱**: <EMAIL>  
> **配置时间**: 2025-08-13

---

## ✅ 已完成的配置

### 🔧 Git全局配置
```bash
✅ 用户名: sanmu
✅ 邮箱: <EMAIL>
```

验证命令：
```powershell
git config --global --list | findstr user
```

---

## 🔑 GitHub Personal Access Token 设置

### 步骤1: 创建Personal Access Token

1. **访问GitHub设置**
   - 登录 [GitHub.com](https://github.com)
   - 点击右上角头像 → Settings
   - 左侧菜单选择 "Developer settings"
   - 选择 "Personal access tokens" → "Tokens (classic)"

2. **创建新Token**
   - 点击 "Generate new token" → "Generate new token (classic)"
   - **Note**: 填写 "AI Assistant MCP Integration"
   - **Expiration**: 建议选择 "90 days" 或 "No expiration"
   - **Scopes**: 选择以下权限：
     ```
     ✅ repo (Full control of private repositories)
     ✅ workflow (Update GitHub Action workflows)
     ✅ write:packages (Upload packages to GitHub Package Registry)
     ✅ delete:packages (Delete packages from GitHub Package Registry)
     ✅ admin:org (Full control of orgs and teams)
     ✅ admin:public_key (Full control of user public keys)
     ✅ admin:repo_hook (Full control of repository hooks)
     ✅ admin:org_hook (Full control of organization hooks)
     ✅ gist (Create gists)
     ✅ notifications (Access notifications)
     ✅ user (Update ALL user data)
     ✅ delete_repo (Delete repositories)
     ✅ write:discussion (Write team discussions)
     ✅ read:discussion (Read team discussions)
     ```

3. **复制Token**
   - 点击 "Generate token"
   - **重要**: 立即复制token，页面刷新后将无法再次查看

### 步骤2: 配置环境变量

#### 方法一：PowerShell临时设置
```powershell
# 设置当前会话的环境变量
$env:GITHUB_TOKEN = "ghp_your_token_here"

# 验证设置
echo $env:GITHUB_TOKEN
```

#### 方法二：系统环境变量（推荐）
```powershell
# 永久设置用户环境变量
[Environment]::SetEnvironmentVariable("GITHUB_TOKEN", "ghp_your_token_here", "User")

# 重启PowerShell后验证
echo $env:GITHUB_TOKEN
```

#### 方法三：.env文件
```bash
# 在项目根目录创建 .env 文件
echo "GITHUB_TOKEN=ghp_your_token_here" > .env

# 确保 .env 在 .gitignore 中
echo ".env" >> .gitignore
```

---

## 🔧 MCP服务器配置

### Claude Desktop配置

1. **找到配置文件位置**
```powershell
# Windows配置文件路径
$configPath = "$env:APPDATA\Claude\claude_desktop_config.json"
echo "配置文件位置: $configPath"
```

2. **复制配置文件**
```powershell
# 确保目录存在
New-Item -ItemType Directory -Path "$env:APPDATA\Claude" -Force

# 复制配置文件
Copy-Item "claude-desktop-config.json" "$env:APPDATA\Claude\claude_desktop_config.json" -Force

Write-Host "✅ Claude Desktop MCP配置已完成" -ForegroundColor Green
```

### VS Code配置
```powershell
# VS Code配置已自动完成
if (Test-Path ".vscode\settings.json") {
    Write-Host "✅ VS Code MCP配置已完成" -ForegroundColor Green
} else {
    Write-Host "❌ VS Code配置缺失" -ForegroundColor Red
}
```

---

## 🧪 测试GitHub集成

### 基本Git操作测试
```powershell
# 检查Git配置
git config --list | findstr user

# 测试GitHub连接
git ls-remote https://github.com/sanmu/test-repo.git 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ GitHub连接正常" -ForegroundColor Green
} else {
    Write-Host "⚠️ GitHub连接需要验证" -ForegroundColor Yellow
}
```

### SSH密钥设置（可选但推荐）

1. **生成SSH密钥**
```powershell
# 生成新的SSH密钥
ssh-keygen -t ed25519 -C "<EMAIL>" -f "$env:USERPROFILE\.ssh\id_ed25519"

# 启动ssh-agent
Start-Service ssh-agent

# 添加密钥到ssh-agent
ssh-add "$env:USERPROFILE\.ssh\id_ed25519"
```

2. **添加公钥到GitHub**
```powershell
# 复制公钥到剪贴板
Get-Content "$env:USERPROFILE\.ssh\id_ed25519.pub" | Set-Clipboard

Write-Host "✅ SSH公钥已复制到剪贴板" -ForegroundColor Green
Write-Host "请访问 https://github.com/settings/ssh/new 添加SSH密钥" -ForegroundColor Yellow
```

3. **测试SSH连接**
```powershell
# 测试SSH连接
ssh -T **************
```

---

## 🎯 GitHub工作流配置

### 推荐的Git配置
```powershell
# 设置默认分支名
git config --global init.defaultBranch main

# 设置推送策略
git config --global push.default simple

# 启用颜色输出
git config --global color.ui auto

# 设置编辑器
git config --global core.editor "code --wait"

# 设置换行符处理（Windows）
git config --global core.autocrlf true

# 设置长路径支持
git config --global core.longpaths true
```

### GitHub CLI安装（可选）
```powershell
# 使用winget安装GitHub CLI
winget install --id GitHub.cli

# 或使用Chocolatey
# choco install gh

# 验证安装
gh --version

# 登录GitHub
gh auth login
```

---

## 🔐 安全最佳实践

### Token安全
- ✅ **使用环境变量**: 不要在代码中硬编码token
- ✅ **定期轮换**: 建议每90天更新token
- ✅ **最小权限**: 只授予必要的权限
- ✅ **监控使用**: 定期检查token使用情况

### 仓库安全
```powershell
# 检查敏感文件
function Test-SensitiveFiles {
    $sensitivePatterns = @("*.key", "*.pem", ".env", "config.json")
    
    foreach ($pattern in $sensitivePatterns) {
        $files = Get-ChildItem -Recurse -Include $pattern 2>$null
        if ($files) {
            Write-Host "⚠️ 发现敏感文件: $($files.Name -join ', ')" -ForegroundColor Yellow
            Write-Host "请确保这些文件在 .gitignore 中" -ForegroundColor Yellow
        }
    }
}

# 运行安全检查
Test-SensitiveFiles
```

---

## 🚀 快速设置脚本

```powershell
# GitHub完整设置脚本
function Set-GitHubConfiguration {
    Write-Host "🐙 GitHub配置设置" -ForegroundColor Green
    Write-Host "=================" -ForegroundColor Green
    
    # 1. 验证Git配置
    Write-Host "📋 当前Git配置:" -ForegroundColor Blue
    git config --global user.name
    git config --global user.email
    
    # 2. 设置GitHub Token（如果提供）
    if ($env:GITHUB_TOKEN) {
        Write-Host "✅ GitHub Token已设置" -ForegroundColor Green
    } else {
        Write-Host "⚠️ GitHub Token未设置，某些功能可能不可用" -ForegroundColor Yellow
        Write-Host "设置方法: `$env:GITHUB_TOKEN = 'your_token'" -ForegroundColor Yellow
    }
    
    # 3. 复制Claude Desktop配置
    if (Test-Path "claude-desktop-config.json") {
        $claudeConfigDir = "$env:APPDATA\Claude"
        New-Item -ItemType Directory -Path $claudeConfigDir -Force | Out-Null
        Copy-Item "claude-desktop-config.json" "$claudeConfigDir\claude_desktop_config.json" -Force
        Write-Host "✅ Claude Desktop MCP配置已完成" -ForegroundColor Green
    }
    
    # 4. 验证配置
    Write-Host "🧪 配置验证:" -ForegroundColor Blue
    if (Test-Path ".vscode\settings.json") {
        Write-Host "  ✅ VS Code MCP配置: 已完成" -ForegroundColor Green
    }
    if (Test-Path "$env:APPDATA\Claude\claude_desktop_config.json") {
        Write-Host "  ✅ Claude Desktop MCP配置: 已完成" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "🎉 GitHub配置完成！" -ForegroundColor Green
    Write-Host "💡 重启AI助手应用以加载新配置" -ForegroundColor Cyan
}

# 运行配置
Set-GitHubConfiguration
```

---

**🎊 GitHub配置已完成！现在您可以享受完整的Git和GitHub集成功能了！**
