<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Packaging Solutions - Fixed Applications</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 40px;
        }

        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .solution-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            height: 400px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .solution-card:nth-child(1) { background: #ff6b35; }
        .solution-card:nth-child(2) { background: #4caf50; }
        .solution-card:nth-child(3) { background: #2196f3; }
        .solution-card:nth-child(4) { background: #9c27b0; }

        .card-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .card-title {
            color: white;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .card-subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 0.9rem;
        }

        .product-features {
            flex: 1;
            margin-bottom: 20px;
        }

        .product-features ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .product-features li {
            color: white;
            margin-bottom: 12px;
            padding-left: 20px;
            position: relative;
            font-size: 0.95rem;
            line-height: 1.4;
            opacity: 1;
            transition: all 0.3s ease;
        }

        .product-features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: rgba(255,255,255,0.9);
            font-weight: bold;
        }

        /* 默认状态：隐藏第4、5个特性 */
        .product-features li:nth-child(n+4) {
            opacity: 0;
            font-size: 0;
            margin-bottom: 0;
            transition: all 0.3s ease;
        }

        /* 渐变遮罩效果 */
        .product-features::after {
            content: "";
            position: absolute;
            bottom: 80px;
            left: 25px;
            right: 25px;
            height: 30px;
            background: linear-gradient(transparent, rgba(255,255,255,0.3));
            pointer-events: none;
            opacity: 1;
            transition: all 0.3s ease;
        }

        .applications {
            background: rgba(255,255,255,0.15);
            border-radius: 8px;
            padding: 15px;
            margin: 0 -25px -25px -25px;
            transition: all 0.3s ease;
        }

        .applications h4 {
            color: white;
            margin: 0 0 8px 0;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .applications p {
            color: rgba(255,255,255,0.9);
            margin: 0;
            font-size: 0.9rem;
            line-height: 1.4;
            transition: all 0.3s ease;
        }

        /* 悬停效果：压缩Applications，显示完整特性 */
        .solution-card:hover .applications {
            padding: 8px 15px 10px 15px !important;
            background: rgba(255,255,255,0.1) !important;
        }

        .solution-card:hover .applications h4 {
            font-size: 0.85rem !important;
            margin-bottom: 4px !important;
        }

        .solution-card:hover .applications p {
            font-size: 0.8rem !important;
            line-height: 1.2 !important;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .solution-card:hover .product-features {
            margin-bottom: 10px !important;
        }

        .solution-card:hover .product-features li:nth-child(n+4) {
            opacity: 1 !important;
            font-size: 0.95rem !important;
            margin-bottom: 12px !important;
            transition-delay: 0.1s !important;
        }

        .solution-card:hover .product-features::after {
            opacity: 0 !important;
        }

        .solution-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Packaging Solutions</h1>
        <p class="subtitle">Hover over cards to see complete features list - Applications section will compress to make space</p>
        
        <div class="solutions-grid">
            <div class="solution-card">
                <div class="card-header">
                    <h3 class="card-title">High-Speed Timing Belts</h3>
                    <p class="card-subtitle">Up to 50 m/s</p>
                </div>
                
                <div class="product-features">
                    <ul>
                        <li>Low stretch polyurethane construction</li>
                        <li>Precision-ground tooth profile</li>
                        <li>Dynamic balancing for smooth operation</li>
                        <li>Reduced vibration and noise</li>
                        <li>Extended fatigue life</li>
                    </ul>
                </div>
                
                <div class="applications">
                    <h4>Applications:</h4>
                    <p>High-speed packaging machinery, automated sorting systems, conveyor belt applications requiring precise timing and minimal maintenance.</p>
                </div>
            </div>

            <div class="solution-card">
                <div class="card-header">
                    <h3 class="card-title">Food-Grade Belts</h3>
                    <p class="card-subtitle">FDA Compliant</p>
                </div>
                
                <div class="product-features">
                    <ul>
                        <li>FDA 21 CFR 177.2600 approved materials</li>
                        <li>Smooth, non-porous surface</li>
                        <li>Resistant to oils and cleaning agents</li>
                        <li>Blue color for contamination detection</li>
                        <li>Temperature range: -20°C to +80°C</li>
                    </ul>
                </div>
                
                <div class="applications">
                    <h4>Applications:</h4>
                    <p>Food processing lines, beverage packaging, pharmaceutical manufacturing, and any application requiring strict hygiene standards.</p>
                </div>
            </div>

            <div class="solution-card">
                <div class="card-header">
                    <h3 class="card-title">Precision Positioning Belts</h3>
                    <p class="card-subtitle">±0.05mm Accuracy</p>
                </div>
                
                <div class="product-features">
                    <ul>
                        <li>Ultra-precise tooth geometry</li>
                        <li>Minimal backlash design</li>
                        <li>Superior dimensional stability</li>
                        <li>Consistent tracking performance</li>
                        <li>Low coefficient of friction</li>
                    </ul>
                </div>
                
                <div class="applications">
                    <h4>Applications:</h4>
                    <p>Precision labeling machines, pick-and-place systems, robotic packaging cells, and high-accuracy positioning applications.</p>
                </div>
            </div>

            <div class="solution-card">
                <div class="card-header">
                    <h3 class="card-title">Cleanroom Belts</h3>
                    <p class="card-subtitle">ISO Class 5</p>
                </div>
                
                <div class="product-features">
                    <ul>
                        <li>Low particle generation materials</li>
                        <li>Antistatic properties available</li>
                        <li>Chemical resistant compounds</li>
                        <li>Smooth edge finish</li>
                        <li>Validation documentation provided</li>
                    </ul>
                </div>
                
                <div class="applications">
                    <h4>Applications:</h4>
                    <p>Semiconductor packaging, medical device assembly, pharmaceutical cleanrooms, and electronics manufacturing environments.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
