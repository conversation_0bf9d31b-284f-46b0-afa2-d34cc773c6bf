# 项目上下文信息

- 用户使用VS Code环境，需要在test文件夹中的word项目里调用rules文件夹中的规则文件
- 用户还有两个MCP服务需要集成到规则文件：Playwright(浏览器自动化)和Sequential thinking(序列思考)
- 当前日期：2025年8月25日。用户需要为 galaxyticketing.com（澳门票务平台）开发购票脚本。网站主要销售演出和活动门票，典型流程：浏览活动→选择场次→选择票数→填写信息→支付→确认出票。技术挑战包括反爬虫检测、高并发限制、库存实时性、验证码机制等。
- Galaxy Ticket Bot脚本启动命令：在jiaoben目录下运行 npm start 或 node src/index.js，脚本已成功启动并显示交互菜单
- Galaxy Ticket Bot已完成全面修复：1)修复浏览器显示问题(1920x1080分辨率)；2)升级活动选择逻辑(智能匹配算法)；3)新增票价选择功能(5种策略)；4)优化购票流程。用户选择重启程序测试修复效果
