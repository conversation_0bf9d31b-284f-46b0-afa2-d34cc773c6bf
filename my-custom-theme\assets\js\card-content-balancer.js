/**
 * {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
 * Card Content Balancer - Dynamic Content Balancing Algorithm
 * Provides intelligent content alignment and interaction enhancement for solution cards
 */

class CardContentBalancer {
    constructor(containerSelector = '.solutions-grid') {
        this.container = document.querySelector(containerSelector);
        if (!this.container) return;
        
        this.cards = this.container.querySelectorAll('.solution-category');
        if (this.cards.length === 0) return;
        
        this.init();
    }
    
    init() {
        this.analyzeContent();
        this.applyBalancing();
        this.addInteractivity();
        this.addAccessibility();
    }
    
    /**
     * {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
     * Analyze card content density
     */
    analyzeContent() {
        this.contentMetrics = Array.from(this.cards).map(card => {
            const features = card.querySelectorAll('.product-features li');
            const appText = card.querySelector('.applications p');
            const title = card.querySelector('h3');
            
            return {
                card,
                featureCount: features.length,
                textLength: appText ? appText.textContent.length : 0,
                titleLength: title ? title.textContent.length : 0,
                features: Array.from(features)
            };
        });
        
        // 计算平均值
        this.avgFeatures = this.contentMetrics.reduce((sum, m) => sum + m.featureCount, 0) / this.contentMetrics.length;
        this.avgTextLength = this.contentMetrics.reduce((sum, m) => sum + m.textLength, 0) / this.contentMetrics.length;
        this.avgTitleLength = this.contentMetrics.reduce((sum, m) => sum + m.titleLength, 0) / this.contentMetrics.length;
    }
    
    /**
     * {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
     * Apply content balancing strategy
     */
    applyBalancing() {
        this.contentMetrics.forEach(metric => {
            const { card, featureCount, textLength, titleLength } = metric;
            
            // 为特性数量设置data属性
            card.setAttribute('data-features', featureCount);
            
            // 为短标题的卡片增加视觉权重
            if (titleLength < this.avgTitleLength * 0.8) {
                const title = card.querySelector('h3');
                if (title) {
                    title.style.fontSize = '1.4rem';
                    title.style.fontWeight = '700';
                }
            }
            
            // 为短文本的卡片增加背景强调
            if (textLength < this.avgTextLength * 0.8) {
                const appsSection = card.querySelector('.applications');
                if (appsSection) {
                    appsSection.style.background = 'rgba(156, 39, 176, 0.08)';
                    appsSection.style.borderTop = '3px solid rgba(156, 39, 176, 0.3)';
                }
            }
            
            // 为特性较少的卡片调整间距
            if (featureCount < this.avgFeatures) {
                const features = card.querySelectorAll('.product-features li');
                features.forEach(li => {
                    li.style.marginBottom = '12px';
                    li.style.fontWeight = '500';
                });
            }
        });
    }
    
    /**
     * 添加交互功能
     */
    addInteractivity() {
        this.cards.forEach((card, index) => {
            // 添加点击展开功能（移动端友好）
            card.addEventListener('click', (e) => {
                // 避免在链接或按钮上触发
                if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON') return;

                this.toggleCardExpansion(card);
            });

            // 添加键盘导航支持
            card.setAttribute('tabindex', '0');
            card.setAttribute('role', 'button');
            card.setAttribute('aria-label', `View details for ${card.querySelector('h3')?.textContent || 'solution'}`);

            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleCardExpansion(card);
                }
            });

            // 添加进入动画延迟
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in-up');
        });
    }
    
    /**
     * 切换卡片展开状态
     */
    toggleCardExpansion(card) {
        const isExpanded = card.classList.contains('expanded');

        // 移除其他卡片的展开状态
        this.cards.forEach(c => c.classList.remove('expanded'));

        if (!isExpanded) {
            card.classList.add('expanded');
            card.setAttribute('aria-expanded', 'true');

            // 滚动到卡片位置
            card.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        } else {
            card.setAttribute('aria-expanded', 'false');
        }
    }
    
    /**
     * 添加无障碍支持
     */
    addAccessibility() {
        this.cards.forEach(card => {
            const features = card.querySelectorAll('.product-features li');
            const hiddenFeatures = Array.from(features).slice(3);
            
            // 为隐藏的特性添加aria-hidden
            hiddenFeatures.forEach(feature => {
                feature.setAttribute('aria-hidden', 'true');
            });
            
            // 添加展开提示
            const expandHint = document.createElement('span');
            expandHint.className = 'sr-only';
            expandHint.textContent = 'Click or press Enter to view all features';
            card.appendChild(expandHint);
        });
    }
    
    /**
     * 响应式调整
     */
    handleResize() {
        // 重新分析和平衡内容
        this.analyzeContent();
        this.applyBalancing();
    }
}

// 添加CSS动画类
const style = document.createElement('style');
style.textContent = `
    .fade-in-up {
        animation: fadeInUp 0.6s ease forwards;
        opacity: 0;
        transform: translateY(30px);
    }

    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .solution-category.expanded .product-features {
        max-height: none !important;
    }

    .solution-category.expanded .product-features li:nth-child(n+4) {
        opacity: 1 !important;
        font-size: 1em !important;
    }

    .solution-category.expanded .product-features::after {
        display: none !important;
    }

    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
        .solution-category {
            cursor: pointer;
        }

        .solution-category:hover .product-features {
            max-height: 140px !important;
        }

        .solution-category.expanded .product-features {
            max-height: none !important;
        }
    }
`;
document.head.appendChild(style);

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    // 为包装行业页面初始化
    if (document.querySelector('.packaging-industry')) {
        const balancer = new CardContentBalancer('.packaging-industry .solutions-grid');
        
        // 响应式处理
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                balancer.handleResize();
            }, 250);
        });
    }
});

// 导出类供其他页面使用
window.CardContentBalancer = CardContentBalancer;
