/**
 * {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
 * Simplified Contact Form - Ensures basic functionality works properly
 */

// {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
// Simple initialization function
function initContactBox() {
    console.log('=== Contact Box Debug ===');

    // 查找元素
    const trigger = document.querySelector('.contact-box-trigger');
    const modal = document.querySelector('.contact-modal');
    const form = document.querySelector('#contact-form');

    console.log('Elements found:', {
        trigger: !!trigger,
        modal: !!modal,
        form: !!form
    });

    if (!trigger || !modal) {
        console.log('Missing elements, will retry...');
        return false;
    }

    // 添加点击事件
    trigger.addEventListener('click', function(e) {
        console.log('Contact button clicked!');
        e.preventDefault();

        // 显示模态框
        modal.style.display = 'flex';
        modal.classList.add('active');

        // 防止背景滚动
        document.body.style.overflow = 'hidden';

        console.log('Modal should be visible now');
    });

    // 关闭按钮
    const closeBtn = modal.querySelector('.contact-form-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            console.log('Close button clicked');
            closeModal();
        });
    }

    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            console.log('Background clicked');
            closeModal();
        }
    });

    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('active')) {
            console.log('ESC pressed');
            closeModal();
        }
    });

    function closeModal() {
        modal.classList.remove('active');
        modal.style.display = 'none';
        document.body.style.overflow = '';
        console.log('Modal closed');
    }

    console.log('Contact box initialized successfully!');
    return true;
}

// 尝试初始化，如果失败则重试
function tryInit() {
    if (initContactBox()) {
        console.log('Contact box initialization successful');
        return;
    }

    // 如果失败，等待一段时间后重试
    setTimeout(tryInit, 500);
}

// 启动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', tryInit);
} else {
    tryInit();
}

// 额外保险：window load后再试一次
window.addEventListener('load', tryInit);