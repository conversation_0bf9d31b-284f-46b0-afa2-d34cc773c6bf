#!/usr/bin/env node

/**
 * Galaxy Ticket Bot - 主入口文件
 * 混合优化方案：Playwright + Fingerprint Suite + Stealth + 自定义优化
 * 
 * 使用方法:
 * npm start
 * node src/index.js
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import { config, getConfigSummary, validateConfig } from './config/config.js';
import TicketBot from './core/ticket-bot.js';
import logger from './utils/logger.js';

/**
 * 主应用类
 */
class GalaxyTicketBotApp {
  constructor() {
    this.ticketBot = new TicketBot();
    this.isRunning = false;
  }

  /**
   * 启动应用
   */
  async start() {
    try {
      // 显示欢迎信息
      this.showWelcome();

      // 验证配置
      const configValidation = validateConfig();
      if (!configValidation.isValid) {
        logger.error('配置验证失败', { errors: configValidation.errors });
        console.log(chalk.red('\n❌ 配置错误:'));
        configValidation.errors.forEach(error => {
          console.log(chalk.red(`  • ${error}`));
        });
        console.log(chalk.yellow('\n请检查 .env 文件配置\n'));
        process.exit(1);
      }

      // 显示配置摘要
      this.showConfigSummary();

      // 显示主菜单
      await this.showMainMenu();

    } catch (error) {
      logger.error('应用启动失败', { error: error.message });
      console.log(chalk.red(`\n❌ 启动失败: ${error.message}\n`));
      process.exit(1);
    }
  }

  /**
   * 显示欢迎信息
   */
  showWelcome() {
    console.clear();
    console.log(chalk.cyan(`
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🎫 Galaxy Ticket Bot - 混合优化方案                        ║
║                                                              ║
║    🛡️  三重反检测保护 (Fingerprint + Stealth + 自定义)        ║
║    ⚡  智能重试机制                                           ║
║    🎭  动态身份切换                                           ║
║    📊  实时状态监控                                           ║
║                                                              ║
║    版本: ${config.app.version}                                        ║
║    目标: ${config.ticket.targetUrl}                           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    `));
  }

  /**
   * 显示配置摘要
   */
  showConfigSummary() {
    const summary = getConfigSummary();
    
    console.log(chalk.blue('\n📋 当前配置:'));
    console.log(chalk.gray('─'.repeat(50)));
    
    Object.entries(summary).forEach(([key, value]) => {
      if (typeof value === 'object') {
        console.log(chalk.blue(`${key}:`));
        Object.entries(value).forEach(([subKey, subValue]) => {
          console.log(chalk.gray(`  ${subKey}: ${chalk.white(subValue)}`));
        });
      } else {
        console.log(chalk.gray(`${key}: ${chalk.white(value)}`));
      }
    });
    
    console.log(chalk.gray('─'.repeat(50)));
  }

  /**
   * 显示主菜单
   */
  async showMainMenu() {
    const choices = [
      {
        name: '🚀 开始购票',
        value: 'start',
      },
      {
        name: '⚙️  配置设置',
        value: 'config',
      },
      {
        name: '📊 系统状态',
        value: 'status',
      },
      {
        name: '🧪 测试连接',
        value: 'test',
      },
      {
        name: '📖 使用帮助',
        value: 'help',
      },
      {
        name: '❌ 退出程序',
        value: 'exit',
      },
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '请选择操作:',
        choices,
      },
    ]);

    await this.handleMenuAction(action);
  }

  /**
   * 处理菜单操作
   */
  async handleMenuAction(action) {
    switch (action) {
      case 'start':
        await this.startTicketing();
        break;
      case 'config':
        await this.showConfigMenu();
        break;
      case 'status':
        await this.showStatus();
        break;
      case 'test':
        await this.testConnection();
        break;
      case 'help':
        await this.showHelp();
        break;
      case 'exit':
        await this.exit();
        break;
      default:
        await this.showMainMenu();
    }
  }

  /**
   * 开始购票
   */
  async startTicketing() {
    if (this.isRunning) {
      console.log(chalk.yellow('\n⚠️  购票流程已在运行中\n'));
      await this.showMainMenu();
      return;
    }

    try {
      // 获取购票配置
      const ticketConfig = await this.getTicketConfig();
      
      console.log(chalk.green('\n🚀 启动购票流程...\n'));
      
      const spinner = ora('正在初始化浏览器...').start();
      this.isRunning = true;

      // 启动购票
      const result = await this.ticketBot.start(ticketConfig);

      spinner.stop();

      if (result.success) {
        console.log(chalk.green('\n🎉 购票流程完成!'));
        if (result.data?.paymentRequired) {
          console.log(chalk.yellow('💳 请在浏览器中完成支付流程'));
        }
      } else {
        console.log(chalk.red(`\n❌ 购票失败: ${result.error}`));
      }

    } catch (error) {
      console.log(chalk.red(`\n❌ 购票异常: ${error.message}`));
    } finally {
      this.isRunning = false;
      
      const { continueAction } = await inquirer.prompt([
        {
          type: 'list',
          name: 'continueAction',
          message: '接下来要做什么?',
          choices: [
            { name: '🔄 重新购票', value: 'retry' },
            { name: '📋 返回主菜单', value: 'menu' },
            { name: '❌ 退出程序', value: 'exit' },
          ],
        },
      ]);

      if (continueAction === 'retry') {
        await this.startTicketing();
      } else if (continueAction === 'menu') {
        await this.showMainMenu();
      } else {
        await this.exit();
      }
    }
  }

  /**
   * 获取购票配置
   */
  async getTicketConfig() {
    const questions = [];

    // 如果没有配置用户信息，询问
    if (!config.ticket.userInfo.name) {
      questions.push({
        type: 'input',
        name: 'userName',
        message: '请输入姓名:',
        validate: input => input.trim() !== '' || '姓名不能为空',
      });
    }

    if (!config.ticket.userInfo.email) {
      questions.push({
        type: 'input',
        name: 'userEmail',
        message: '请输入邮箱:',
        validate: input => {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          return emailRegex.test(input) || '请输入有效的邮箱地址';
        },
      });
    }

    if (!config.ticket.userInfo.phone) {
      questions.push({
        type: 'input',
        name: 'userPhone',
        message: '请输入电话号码:',
        validate: input => input.trim() !== '' || '电话号码不能为空',
      });
    }

    // 询问特定活动
    questions.push({
      type: 'input',
      name: 'eventName',
      message: '指定活动名称 (留空选择第一个):',
    });

    // 询问票数
    questions.push({
      type: 'number',
      name: 'quantity',
      message: '购票数量:',
      default: config.ticket.quantity,
      validate: input => input > 0 || '票数必须大于0',
    });

    // 询问票价范围
    questions.push({
      type: 'list',
      name: 'priceRangeType',
      message: '票价选择方式:',
      choices: [
        { name: '任意价格 (选择第一个可用)', value: 'any' },
        { name: '指定价格范围', value: 'range' },
        { name: '最低价格', value: 'lowest' },
        { name: '最高价格', value: 'highest' },
        { name: '精确价格', value: 'exact' },
      ],
      default: 'any',
    });

    const answers = await inquirer.prompt(questions);

    // 根据价格选择方式询问具体价格
    let priceRange = null;
    if (answers.priceRangeType !== 'any') {
      const priceQuestions = [];

      switch (answers.priceRangeType) {
        case 'range':
          priceQuestions.push({
            type: 'input',
            name: 'priceRange',
            message: '请输入价格范围 (格式: 100-500):',
            validate: input => {
              const rangeRegex = /^\d+-\d+$/;
              return rangeRegex.test(input) || '请输入正确格式，如: 100-500';
            },
          });
          break;
        case 'lowest':
          priceRange = '<=999999'; // 选择最低价格
          break;
        case 'highest':
          priceRange = '>=0'; // 选择最高价格
          break;
        case 'exact':
          priceQuestions.push({
            type: 'number',
            name: 'exactPrice',
            message: '请输入精确价格:',
            validate: input => input > 0 || '价格必须大于0',
          });
          break;
      }

      if (priceQuestions.length > 0) {
        const priceAnswers = await inquirer.prompt(priceQuestions);
        if (answers.priceRangeType === 'range') {
          priceRange = priceAnswers.priceRange;
        } else if (answers.priceRangeType === 'exact') {
          priceRange = priceAnswers.exactPrice.toString();
        }
      }
    }

    return {
      userInfo: {
        name: answers.userName || config.ticket.userInfo.name,
        email: answers.userEmail || config.ticket.userInfo.email,
        phone: answers.userPhone || config.ticket.userInfo.phone,
      },
      eventName: answers.eventName || null,
      quantity: answers.quantity || config.ticket.quantity,
      priceRange: priceRange,
      priceRangeType: answers.priceRangeType,
    };
  }

  /**
   * 显示配置菜单
   */
  async showConfigMenu() {
    console.log(chalk.blue('\n⚙️  配置设置'));
    console.log(chalk.gray('当前配置文件: .env'));
    console.log(chalk.yellow('请直接编辑 .env 文件来修改配置\n'));
    
    await this.showMainMenu();
  }

  /**
   * 显示系统状态
   */
  async showStatus() {
    console.log(chalk.blue('\n📊 系统状态'));
    console.log(chalk.gray('─'.repeat(50)));

    const status = this.ticketBot.getStatus();
    
    console.log(chalk.gray(`运行状态: ${status.isRunning ? chalk.green('运行中') : chalk.red('已停止')}`));
    console.log(chalk.gray(`重试次数: ${status.retryCount}/${status.maxRetries}`));
    
    if (status.startTime) {
      console.log(chalk.gray(`运行时长: ${Math.floor(status.duration / 1000)}秒`));
    }

    if (status.browserStatus) {
      console.log(chalk.gray(`浏览器: ${status.browserStatus.initialized ? chalk.green('已初始化') : chalk.red('未初始化')}`));
      console.log(chalk.gray(`上下文数: ${status.browserStatus.contextsCount}`));
      console.log(chalk.gray(`页面数: ${status.browserStatus.pagesCount}`));
    }

    console.log(chalk.gray('─'.repeat(50)));
    console.log();
    
    await this.showMainMenu();
  }

  /**
   * 测试连接
   */
  async testConnection() {
    console.log(chalk.blue('\n🧪 测试连接'));
    
    const spinner = ora('正在测试目标网站连接...').start();
    
    try {
      // 这里可以添加简单的连接测试
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      spinner.succeed('连接测试成功');
      console.log(chalk.green(`✅ 目标网站: ${config.ticket.targetUrl}`));
      
      if (config.proxy.server) {
        console.log(chalk.green(`✅ 代理服务器: ${config.proxy.server}`));
      }
      
    } catch (error) {
      spinner.fail('连接测试失败');
      console.log(chalk.red(`❌ 错误: ${error.message}`));
    }
    
    console.log();
    await this.showMainMenu();
  }

  /**
   * 显示帮助信息
   */
  async showHelp() {
    console.log(chalk.blue('\n📖 使用帮助'));
    console.log(chalk.gray('─'.repeat(50)));
    console.log(`
${chalk.yellow('配置文件:')}
  • 复制 .env.example 为 .env
  • 根据需要修改配置参数
  • 重启程序使配置生效

${chalk.yellow('代理设置:')}
  • PROXY_SERVER=host:port
  • PROXY_USERNAME=用户名 (可选)
  • PROXY_PASSWORD=密码 (可选)

${chalk.yellow('反检测设置:')}
  • ENABLE_FINGERPRINT=true (启用指纹伪装)
  • ENABLE_STEALTH=true (启用隐身模式)
  • DEVICE_TYPE=desktop|mobile
  • OS_TYPE=windows|macos|linux

${chalk.yellow('购票设置:')}
  • TARGET_URL=目标网站地址
  • TICKET_QUANTITY=购票数量
  • USER_NAME=姓名
  • USER_EMAIL=邮箱
  • USER_PHONE=电话

${chalk.yellow('注意事项:')}
  • 请确保网络连接稳定
  • 建议使用代理以提高成功率
  • 支付环节需要手动完成
  • 遵守网站使用条款
    `);
    console.log(chalk.gray('─'.repeat(50)));
    console.log();
    
    await this.showMainMenu();
  }

  /**
   * 退出程序
   */
  async exit() {
    console.log(chalk.yellow('\n👋 正在退出程序...'));
    
    if (this.isRunning) {
      await this.ticketBot.stop();
    }
    
    console.log(chalk.green('✅ 程序已安全退出\n'));
    process.exit(0);
  }
}

/**
 * 处理未捕获的异常
 */
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常', { error: error.message, stack: error.stack });
  console.log(chalk.red(`\n💥 程序异常: ${error.message}\n`));
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝', { reason, promise });
  console.log(chalk.red(`\n💥 Promise异常: ${reason}\n`));
  process.exit(1);
});

/**
 * 处理程序退出信号
 */
process.on('SIGINT', async () => {
  console.log(chalk.yellow('\n\n🛑 收到退出信号，正在清理资源...'));
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log(chalk.yellow('\n\n🛑 收到终止信号，正在清理资源...'));
  process.exit(0);
});

/**
 * 启动应用
 */
async function main() {
  const app = new GalaxyTicketBotApp();
  await app.start();
}

// 启动应用
main().catch((error) => {
  logger.error('应用启动失败', { error: error.message });
  console.log(chalk.red(`\n💥 启动失败: ${error.message}\n`));
  process.exit(1);
});
