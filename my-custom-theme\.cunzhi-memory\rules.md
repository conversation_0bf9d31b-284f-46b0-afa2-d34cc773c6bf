# 开发规范和规则

- 用户要求严格按照 AURA-X 协议执行：1) 每次都调用 context7 获取最新信息；2) 遵循寸止强制交互网关，所有决策通过寸止MCP确认；3) 不生成总结性Markdown文档；4) 不生成测试脚本；5) 不编译，用户自己编译；6) 帮用户运行代码
- 用户要求严格按照 AURA-X 协议执行，每次都调用 context7，遵循寸止强制交互网关和记忆长期知识库的规则
- 当前导航栏固定定位方法完全不可行，需要重新设计全新的实现方案
- 用户要求严格按照 .augment/rules/mcp.md 文档中的 AURA-X 协议执行，每次都调用 context7，遵循寸止强制交互网关和记忆长期知识库的规则
- 导航栏固定定位问题已修复：1)修复了CSS媒体查询中padding-top被重置为0的问题；2)在移动端和小屏幕媒体查询中强制应用固定定位；3)优化JavaScript执行时机，使用requestAnimationFrame确保DOM渲染完成；4)添加防抖处理窗口大小变化；5)使用!important确保CSS优先级；6)添加transform和backface-visibility优化渲染性能
- 导航栏固定定位问题的根本原因是body元素的contain属性。CSS contain: layout会创建新的包含块，导致position:fixed相对于body而不是视口定位。已移除contain属性，现在导航栏应该能正确固定在视口顶部
- 联系表单问题已修复：1)重新启用了contact-box-script的JavaScript加载；2)重新启用了AJAX本地化数据；3)重新启用了contact_box_html()函数的wp_footer钩子。现在右下角的联系表单应该能正常显示和工作
- 行业子页面联系表单问题已修复：移除了contact-box.css的条件加载限制，现在CSS样式会在所有页面加载，确保联系表单模态框在所有页面都能正确隐藏和显示
- 用户要求严格按照 .augment/rules/mcp.md 文档中的 AURA-X 协议执行，每次都调用 context7，遵循寸止强制交互网关和记忆长期知识库的规则
