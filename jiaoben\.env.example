# Galaxy Ticket Bot 配置文件
# 复制此文件为 .env 并填入您的配置

# ================================
# 基础配置
# ================================
# 是否启用无头模式 (true/false)
HEADLESS=false

# 浏览器类型 (chromium/firefox/webkit)
BROWSER_TYPE=chromium

# 最大重试次数
MAX_RETRIES=3

# 请求超时时间 (毫秒)
TIMEOUT=30000

# ================================
# 代理配置 (用户自己配置)
# ================================
# 代理服务器地址 (可选)
PROXY_SERVER=

# 代理用户名 (可选)
PROXY_USERNAME=

# 代理密码 (可选)
PROXY_PASSWORD=

# ================================
# 反检测配置
# ================================
# 是否启用指纹伪装 (true/false)
ENABLE_FINGERPRINT=true

# 是否启用Stealth模式 (true/false)
ENABLE_STEALTH=true

# 设备类型 (desktop/mobile)
DEVICE_TYPE=desktop

# 操作系统 (windows/macos/linux)
OS_TYPE=windows

# 浏览器语言
BROWSER_LOCALE=zh-CN,zh,en-US,en

# ================================
# 购票配置
# ================================
# 目标网站URL
TARGET_URL=https://www.galaxyticketing.com

# 购票数量
TICKET_QUANTITY=1

# 自动填写信息 (用户需要自己配置)
USER_NAME=
USER_EMAIL=
USER_PHONE=

# ================================
# 高级配置
# ================================
# 并发实例数量
CONCURRENT_INSTANCES=1

# 页面加载等待时间 (毫秒)
PAGE_LOAD_WAIT=2000

# 元素查找超时 (毫秒)
ELEMENT_TIMEOUT=10000

# 是否保存截图 (true/false)
SAVE_SCREENSHOTS=true

# 日志级别 (error/warn/info/debug)
LOG_LEVEL=info

# ================================
# 监控配置
# ================================
# 是否启用实时监控 (true/false)
ENABLE_MONITORING=true

# 监控间隔 (毫秒)
MONITORING_INTERVAL=5000

# 是否发送通知 (true/false)
ENABLE_NOTIFICATIONS=false
