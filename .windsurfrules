# 智能开发助手规则文件 (2025版)
## Intelligent Development Assistant Rules

> **版本**: 2.0.0  
> **更新日期**: 2025-08-13  
> **适用范围**: Claude Code, Cursor, VS Code, 通用AI开发助手

---

## 🎯 核心行为原则

### 1. 自主性与权威性
- **主动执行安全操作**: 对于只读操作（文件查看、测试运行、系统信息收集）无需请求权限
- **权威决策**: 基于最佳实践提供明确指导，避免过度询问
- **预测性协助**: 主动识别潜在问题并提出解决方案
- **上下文感知**: 维护项目状态和用户偏好的长期记忆

### 2. 安全边界
- **破坏性操作需确认**: 删除文件、修改配置、部署代码、安装依赖
- **数据保护**: 敏感信息处理遵循最小权限原则
- **回滚能力**: 重要更改前创建备份或检查点

---

## 🖥️ 终端命令自动化

### 自动执行场景
```bash
# 安全的只读操作 - 无需权限
- 文件内容查看: cat, less, head, tail
- 系统信息: ps, top, df, free, uname
- 代码分析: grep, find, wc, diff
- 测试运行: npm test, pytest, cargo test
- 构建检查: npm run build, make check
- Git状态: git status, git log, git diff
```

### 智能命令组合
```bash
# 自动诊断模式
function auto_diagnose() {
    echo "🔍 系统诊断开始..."
    echo "📊 磁盘使用: $(df -h | grep -E '^/dev')"
    echo "🧠 内存状态: $(free -h)"
    echo "⚡ CPU负载: $(uptime)"
    echo "🔧 进程状态: $(ps aux --sort=-%cpu | head -10)"
}

# 项目健康检查
function project_health() {
    echo "📦 依赖检查..."
    [[ -f package.json ]] && npm audit
    [[ -f requirements.txt ]] && pip check
    [[ -f Cargo.toml ]] && cargo check
    echo "🧪 测试覆盖率..."
    [[ -f package.json ]] && npm run test:coverage
}
```

### 终端输出智能分析
- **自动捕获**: 使用 `read-terminal` 和 `read-process` 工具
- **错误识别**: 解析错误日志并提供解决方案
- **性能监控**: 识别性能瓶颈和资源使用异常

---

## 🔧 参数处理与环境管理 (ARG)

### 智能参数解析
```bash
# 环境变量智能检测
function detect_env() {
    local env_files=(".env" ".env.local" ".env.development" ".env.production")
    for file in "${env_files[@]}"; do
        [[ -f "$file" ]] && echo "📄 发现环境文件: $file"
    done
}

# 配置文件自动识别
function detect_config() {
    local configs=("package.json" "tsconfig.json" "webpack.config.js" "vite.config.ts")
    for config in "${configs[@]}"; do
        [[ -f "$config" ]] && echo "⚙️ 配置文件: $config"
    done
}
```

### 动态参数适配
- **框架检测**: 自动识别React、Vue、Angular、Next.js等
- **构建工具**: 智能选择Webpack、Vite、Rollup、esbuild
- **包管理器**: 优先级 pnpm > yarn > npm
- **运行时**: Node.js版本兼容性检查

---

## 🌐 MCP (模型上下文协议) 集成

### 核心MCP服务器配置
```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    },
    "peekaboo": {
      "command": "npx", 
      "args": ["-y", "@steipete/peekaboo-mcp@beta"],
      "env": {
        "PEEKABOO_AI_PROVIDERS": "openai/gpt-4o,ollama/llava:latest"
      }
    },
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"
      }
    }
  }
}
```

### MCP服务器同步
- **跨平台一致性**: Claude Desktop、Cursor、VS Code配置同步
- **自动更新**: 定期检查MCP服务器版本
- **配置验证**: 启动时验证MCP连接状态

---

## 🚀 主动解决问题

### 预测性分析
```typescript
// 代码质量预测
interface QualityMetrics {
  complexity: number;
  testCoverage: number;
  dependencies: DependencyRisk[];
  securityVulns: SecurityIssue[];
}

// 性能优化建议
interface OptimizationSuggestion {
  type: 'bundle' | 'runtime' | 'memory' | 'network';
  impact: 'high' | 'medium' | 'low';
  effort: 'easy' | 'moderate' | 'complex';
  description: string;
}
```

### 智能调试流程
1. **错误分类**: 语法、逻辑、运行时、配置错误
2. **根因分析**: 追踪错误源头和传播路径
3. **解决方案排序**: 按影响和实施难度排序
4. **验证测试**: 自动生成测试用例验证修复

### 代码重构建议
- **设计模式应用**: 识别可应用的设计模式
- **性能优化**: 算法复杂度、内存使用优化
- **可维护性**: 代码分离、模块化建议
- **现代化升级**: 新语言特性和库的应用

---

## 💡 权威性决策框架

### 技术选型决策树
```mermaid
graph TD
    A[项目需求] --> B{项目规模}
    B -->|小型| C[轻量级方案]
    B -->|中型| D[平衡方案] 
    B -->|大型| E[企业级方案]
    C --> F[Vite + Vue/React]
    D --> G[Next.js/Nuxt.js]
    E --> H[微前端架构]
```

### 最佳实践执行
- **代码规范**: ESLint、Prettier、TypeScript严格模式
- **测试策略**: 单元测试(70%) + 集成测试(20%) + E2E测试(10%)
- **CI/CD流程**: 自动化测试、代码质量检查、安全扫描
- **文档标准**: README、API文档、架构决策记录(ADR)

---

## 🔒 安全性与合规

### 安全检查清单
- [ ] 依赖漏洞扫描 (`npm audit`, `safety check`)
- [ ] 敏感信息检测 (API密钥、密码)
- [ ] HTTPS/TLS配置验证
- [ ] 输入验证和输出编码
- [ ] 访问控制和权限管理

### 数据保护
```typescript
// 敏感数据处理模式
class SecureDataHandler {
  private encrypt(data: string): string {
    // 使用AES-256-GCM加密
  }
  
  private sanitize(input: string): string {
    // XSS和注入攻击防护
  }
  
  private audit(action: string, user: string): void {
    // 操作审计日志
  }
}
```

---

## 🎨 现代开发实践 (2025)

### 前端技术栈
- **框架**: React 18+, Vue 3+, Svelte 5+
- **构建工具**: Vite 5+, Turbopack, esbuild
- **状态管理**: Zustand, Pinia, TanStack Query
- **样式方案**: Tailwind CSS, CSS-in-JS, CSS Modules
- **类型系统**: TypeScript 5.0+, 严格模式

### 后端技术栈  
- **运行时**: Node.js 20+, Deno 2.0+, Bun 1.0+
- **框架**: Fastify, Hono, tRPC, GraphQL
- **数据库**: PostgreSQL, Redis, SQLite, Prisma ORM
- **部署**: Docker, Kubernetes, Serverless Functions

### 开发工具链
- **代码质量**: Biome, ESLint 9+, Prettier 3+
- **测试框架**: Vitest, Jest, Playwright, Cypress
- **监控**: Sentry, DataDog, New Relic
- **文档**: Storybook, Docusaurus, VitePress

---

## ⚡ 性能优化策略

### 前端性能
```typescript
// 代码分割和懒加载
const LazyComponent = lazy(() => import('./HeavyComponent'));

// 虚拟化长列表
import { FixedSizeList as List } from 'react-window';

// 图片优化
<Image
  src="/image.jpg"
  alt="描述"
  width={800}
  height={600}
  loading="lazy"
  placeholder="blur"
/>
```

### 后端性能
```typescript
// 数据库查询优化
const users = await prisma.user.findMany({
  select: { id: true, name: true }, // 只选择需要的字段
  where: { active: true },
  take: 10, // 分页
  skip: offset
});

// 缓存策略
const cached = await redis.get(`user:${id}`);
if (!cached) {
  const user = await db.user.findUnique({ where: { id } });
  await redis.setex(`user:${id}`, 3600, JSON.stringify(user));
}
```

---

## 🐛 错误处理与调试

### 智能错误分析
```typescript
interface ErrorContext {
  timestamp: Date;
  userAgent: string;
  url: string;
  userId?: string;
  stackTrace: string;
  breadcrumbs: Breadcrumb[];
}

class ErrorAnalyzer {
  analyze(error: Error, context: ErrorContext): ErrorSolution {
    // AI驱动的错误分析和解决方案推荐
  }
}
```

### 调试工具集成
- **浏览器**: Chrome DevTools, React DevTools, Vue DevTools
- **Node.js**: --inspect, ndb, clinic.js
- **性能分析**: Lighthouse, WebPageTest, Bundle Analyzer
- **日志聚合**: Winston, Pino, structured logging

---

## 📊 监控与分析

### 关键指标 (KPIs)
- **性能**: Core Web Vitals, TTFB, FCP, LCP
- **可用性**: Uptime, Error Rate, Response Time
- **用户体验**: Bounce Rate, Session Duration, Conversion
- **代码质量**: Test Coverage, Code Complexity, Technical Debt

### 自动化报告
```typescript
// 性能监控自动化
class PerformanceMonitor {
  async generateReport(): Promise<PerformanceReport> {
    const metrics = await this.collectMetrics();
    const analysis = await this.analyzeMetrics(metrics);
    const recommendations = await this.generateRecommendations(analysis);
    
    return {
      metrics,
      analysis,
      recommendations,
      timestamp: new Date()
    };
  }
}
```

---

## 🔄 持续改进

### 学习与适应
- **用户反馈**: 收集和分析用户交互模式
- **性能数据**: 基于实际使用数据优化建议
- **技术趋势**: 跟踪最新技术发展和最佳实践
- **错误模式**: 从历史错误中学习和改进

### 规则更新机制
- **版本控制**: 规则文件版本化管理
- **A/B测试**: 新规则的渐进式部署
- **回滚机制**: 问题规则的快速回滚
- **社区贡献**: 开源协作和知识共享

---

## 📝 实施指南

### 快速开始
1. **环境检查**: 验证开发环境和工具链
2. **配置同步**: 统一MCP服务器和开发工具配置  
3. **规则激活**: 在AI助手中加载此规则文件
4. **功能验证**: 测试自动化功能和响应质量

### 定制化配置
- **项目特定规则**: 根据项目需求调整规则
- **团队协作**: 团队共享的规则和最佳实践
- **个人偏好**: 个性化的开发习惯和工具选择

---

## 🤖 高级AI功能

### 代码生成与重构
```typescript
// 智能代码生成模式
interface CodeGenerationContext {
  language: string;
  framework: string;
  patterns: DesignPattern[];
  constraints: Constraint[];
  testRequirements: TestSpec[];
}

class IntelligentCodeGenerator {
  async generateComponent(spec: ComponentSpec): Promise<GeneratedCode> {
    // 基于规范生成高质量代码
    const code = await this.generateFromSpec(spec);
    const tests = await this.generateTests(code, spec);
    const docs = await this.generateDocumentation(code);

    return { code, tests, docs };
  }
}
```

### 架构决策支持
- **技术债务分析**: 识别和量化技术债务
- **重构优先级**: 基于影响和风险评估重构优先级
- **架构演进**: 渐进式架构改进建议
- **依赖管理**: 智能依赖升级和兼容性检查

### 智能测试生成
```typescript
// 自动化测试生成
class TestGenerator {
  generateUnitTests(sourceCode: string): TestSuite {
    // 分析代码路径和边界条件
    const paths = this.analyzeCodePaths(sourceCode);
    const edgeCases = this.identifyEdgeCases(sourceCode);

    return this.createTestSuite(paths, edgeCases);
  }

  generateIntegrationTests(apiSpec: OpenAPISpec): TestSuite {
    // 基于API规范生成集成测试
  }
}
```

---

## 🔮 预测性维护

### 代码健康度预测
```typescript
interface CodeHealthMetrics {
  complexity: number;
  maintainability: number;
  testability: number;
  performance: number;
  security: number;
}

class PredictiveMaintenance {
  async predictIssues(codebase: Codebase): Promise<PredictedIssue[]> {
    const metrics = await this.analyzeCodeHealth(codebase);
    const trends = await this.analyzeTrends(metrics);

    return this.predictFutureIssues(trends);
  }
}
```

### 性能回归预测
- **基准测试**: 自动化性能基准建立和监控
- **回归检测**: 识别性能下降趋势
- **容量规划**: 基于使用模式预测资源需求
- **优化建议**: 主动的性能优化建议

---

## 🌍 多语言与国际化

### 智能本地化
```typescript
// 国际化自动化
class I18nManager {
  async detectMissingTranslations(sourceCode: string): Promise<string[]> {
    const textNodes = this.extractTextNodes(sourceCode);
    const existingKeys = await this.loadTranslationKeys();

    return textNodes.filter(text => !existingKeys.includes(text));
  }

  async generateTranslations(keys: string[], targetLocales: string[]): Promise<TranslationMap> {
    // AI驱动的翻译生成
  }
}
```

### 多语言代码支持
- **语言检测**: 自动识别项目使用的编程语言
- **语法适配**: 针对不同语言的语法检查和建议
- **最佳实践**: 语言特定的最佳实践应用
- **跨语言重构**: 多语言项目的统一重构

---

## 🔐 高级安全功能

### 安全代码审查
```typescript
class SecurityAuditor {
  async auditCode(sourceCode: string): Promise<SecurityReport> {
    const vulnerabilities = await this.scanVulnerabilities(sourceCode);
    const dataFlows = await this.analyzeDataFlows(sourceCode);
    const permissions = await this.checkPermissions(sourceCode);

    return {
      vulnerabilities,
      dataFlows,
      permissions,
      recommendations: this.generateSecurityRecommendations()
    };
  }
}
```

### 合规性检查
- **GDPR合规**: 数据处理和隐私保护检查
- **SOC2合规**: 安全控制和审计要求
- **HIPAA合规**: 医疗数据处理规范
- **PCI DSS**: 支付卡数据安全标准

### 威胁建模
```typescript
interface ThreatModel {
  assets: Asset[];
  threats: Threat[];
  vulnerabilities: Vulnerability[];
  mitigations: Mitigation[];
}

class ThreatModeler {
  async generateThreatModel(architecture: SystemArchitecture): Promise<ThreatModel> {
    // 自动化威胁建模
  }
}
```

---

## 🚀 DevOps集成

### CI/CD优化
```yaml
# 智能CI/CD配置
name: Intelligent CI/CD
on: [push, pull_request]

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Code Quality Analysis
        run: |
          # 代码质量分析
          npm run lint:check
          npm run type:check
          npm run security:audit

      - name: Performance Testing
        run: |
          # 性能测试
          npm run test:performance
          npm run lighthouse:ci

      - name: Deployment Readiness
        run: |
          # 部署就绪检查
          npm run build:production
          npm run test:e2e
```

### 基础设施即代码
```typescript
// Terraform/CDK集成
class InfrastructureManager {
  async generateInfrastructure(requirements: InfraRequirements): Promise<IaCTemplate> {
    const template = await this.createTemplate(requirements);
    const security = await this.addSecurityControls(template);
    const monitoring = await this.addMonitoring(template);

    return this.optimizeTemplate(template);
  }
}
```

### 容器化与编排
```dockerfile
# 智能Dockerfile生成
FROM node:20-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:20-alpine AS runtime
RUN addgroup -g 1001 -S nodejs && adduser -S nextjs -u 1001
WORKDIR /app
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .
USER nextjs
EXPOSE 3000
CMD ["npm", "start"]
```

---

## 📈 数据驱动决策

### 分析仪表板
```typescript
interface DashboardMetrics {
  codeQuality: QualityMetrics;
  performance: PerformanceMetrics;
  security: SecurityMetrics;
  productivity: ProductivityMetrics;
}

class AnalyticsDashboard {
  async generateInsights(metrics: DashboardMetrics): Promise<Insight[]> {
    const trends = await this.analyzeTrends(metrics);
    const anomalies = await this.detectAnomalies(metrics);
    const recommendations = await this.generateRecommendations(trends, anomalies);

    return this.prioritizeInsights(recommendations);
  }
}
```

### A/B测试框架
```typescript
class ABTestFramework {
  async createExperiment(config: ExperimentConfig): Promise<Experiment> {
    const variants = await this.generateVariants(config);
    const metrics = await this.defineMetrics(config);
    const audience = await this.defineAudience(config);

    return new Experiment(variants, metrics, audience);
  }
}
```

---

## 🎯 个性化与学习

### 用户行为学习
```typescript
class UserBehaviorAnalyzer {
  async learnFromInteractions(interactions: Interaction[]): Promise<UserProfile> {
    const patterns = await this.identifyPatterns(interactions);
    const preferences = await this.extractPreferences(patterns);
    const skills = await this.assessSkillLevel(interactions);

    return new UserProfile(patterns, preferences, skills);
  }
}
```

### 自适应建议系统
- **技能水平适配**: 根据用户技能水平调整建议复杂度
- **偏好学习**: 学习用户的编码风格和工具偏好
- **上下文感知**: 基于当前项目和任务提供相关建议
- **进度跟踪**: 跟踪用户学习进度和技能提升

---

## 🔧 扩展与插件系统

### 插件架构
```typescript
interface Plugin {
  name: string;
  version: string;
  activate(context: PluginContext): void;
  deactivate(): void;
}

class PluginManager {
  private plugins: Map<string, Plugin> = new Map();

  async loadPlugin(pluginPath: string): Promise<void> {
    const plugin = await import(pluginPath);
    this.plugins.set(plugin.name, plugin);
    plugin.activate(this.createContext());
  }
}
```

### 自定义规则引擎
```typescript
class RuleEngine {
  private rules: Rule[] = [];

  addRule(rule: Rule): void {
    this.rules.push(rule);
  }

  async executeRules(context: ExecutionContext): Promise<RuleResult[]> {
    const results = await Promise.all(
      this.rules.map(rule => rule.execute(context))
    );

    return this.prioritizeResults(results);
  }
}
```

---

## 📚 知识管理

### 智能文档生成
```typescript
class DocumentationGenerator {
  async generateAPIDoc(sourceCode: string): Promise<APIDocumentation> {
    const endpoints = await this.extractEndpoints(sourceCode);
    const schemas = await this.extractSchemas(sourceCode);
    const examples = await this.generateExamples(endpoints);

    return new APIDocumentation(endpoints, schemas, examples);
  }
}
```

### 知识图谱构建
- **代码关系映射**: 构建代码组件间的依赖关系图
- **概念关联**: 建立技术概念和实现的关联
- **最佳实践库**: 积累和组织最佳实践知识
- **问题解决方案**: 构建问题-解决方案知识库

---

---

## ✅ 实施检查清单

### 环境准备
- [ ] **开发环境验证**
  - [ ] Node.js 20+ / Python 3.11+ / 其他运行时版本检查
  - [ ] 包管理器配置 (pnpm/yarn/npm)
  - [ ] Git配置和SSH密钥设置
  - [ ] 编辑器插件和扩展安装

- [ ] **MCP服务器配置**
  - [ ] Claude Desktop配置文件创建
  - [ ] Cursor MCP配置同步
  - [ ] VS Code工作区配置
  - [ ] 必要的API密钥配置

- [ ] **安全设置**
  - [ ] 环境变量安全存储
  - [ ] API密钥轮换策略
  - [ ] 访问权限最小化
  - [ ] 审计日志启用

### 功能验证
- [ ] **自动化功能测试**
  - [ ] 终端命令自动执行
  - [ ] 错误检测和分析
  - [ ] 代码质量检查
  - [ ] 性能监控激活

- [ ] **集成测试**
  - [ ] MCP服务器连接测试
  - [ ] 外部API集成验证
  - [ ] 数据库连接检查
  - [ ] 部署流程验证

### 团队协作
- [ ] **规则共享**
  - [ ] 团队规则文件版本控制
  - [ ] 个性化配置管理
  - [ ] 最佳实践文档化
  - [ ] 培训和知识传递

---

## 🛠️ 配置模板

### Claude Desktop配置
```json
{
  "globalShortcut": "Cmd+Shift+Space",
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    },
    "peekaboo": {
      "command": "npx",
      "args": ["-y", "@steipete/peekaboo-mcp@beta"],
      "env": {
        "PEEKABOO_AI_PROVIDERS": "openai/gpt-4o,anthropic/claude-3-5-sonnet-20241022",
        "OPENAI_API_KEY": "${OPENAI_API_KEY}",
        "ANTHROPIC_API_KEY": "${ANTHROPIC_API_KEY}"
      }
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"
      }
    },
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem"],
      "env": {
        "ALLOWED_DIRECTORIES": "/Users/<USER>/projects,/Users/<USER>/documents"
      }
    }
  }
}
```

### VS Code工作区配置
```json
{
  "folders": [
    {
      "path": "."
    }
  ],
  "settings": {
    "mcp": {
      "servers": {
        "context7": {
          "command": "npx",
          "args": ["-y", "@upstash/context7-mcp@latest"]
        },
        "github": {
          "command": "npx",
          "args": ["-y", "@modelcontextprotocol/server-github"],
          "env": {
            "GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"
          }
        }
      }
    },
    "typescript.preferences.includePackageJsonAutoImports": "auto",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": true,
      "source.organizeImports": true
    },
    "files.associations": {
      "*.env.*": "dotenv"
    }
  },
  "extensions": {
    "recommendations": [
      "ms-vscode.vscode-typescript-next",
      "esbenp.prettier-vscode",
      "dbaeumer.vscode-eslint",
      "bradlc.vscode-tailwindcss",
      "ms-playwright.playwright"
    ]
  }
}
```

### 环境变量模板
```bash
# .env.example
# 复制为 .env 并填入实际值

# AI服务API密钥
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
GOOGLE_AI_API_KEY=...

# 开发服务
GITHUB_TOKEN=ghp_...
VERCEL_TOKEN=...
NETLIFY_TOKEN=...

# 数据库连接
DATABASE_URL=postgresql://...
REDIS_URL=redis://...

# 监控和分析
SENTRY_DSN=https://...
DATADOG_API_KEY=...

# 功能开关
ENABLE_ANALYTICS=true
ENABLE_ERROR_REPORTING=true
DEBUG_MODE=false
```

### 项目配置文件
```json
// package.json 扩展配置
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "e2e": "playwright test",
    "e2e:ui": "playwright test --ui",
    "security:audit": "npm audit && snyk test",
    "performance:analyze": "npm run build && npx @next/bundle-analyzer",
    "ai:rules:update": "curl -o INTELLIGENT_ASSISTANT_RULES.md https://raw.githubusercontent.com/your-repo/rules/main/INTELLIGENT_ASSISTANT_RULES.md"
  },
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "pre-push": "npm run type-check && npm run test"
    }
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{json,md,yml,yaml}": [
      "prettier --write"
    ]
  }
}
```

---

## 🔄 更新与维护

### 自动更新机制
```bash
#!/bin/bash
# update-ai-rules.sh
# 自动更新AI助手规则文件

RULES_URL="https://raw.githubusercontent.com/your-repo/ai-rules/main/INTELLIGENT_ASSISTANT_RULES.md"
LOCAL_RULES="./INTELLIGENT_ASSISTANT_RULES.md"
BACKUP_DIR="./backups"

# 创建备份
mkdir -p "$BACKUP_DIR"
if [ -f "$LOCAL_RULES" ]; then
    cp "$LOCAL_RULES" "$BACKUP_DIR/INTELLIGENT_ASSISTANT_RULES_$(date +%Y%m%d_%H%M%S).md"
fi

# 下载最新规则
curl -s "$RULES_URL" -o "$LOCAL_RULES.new"

# 验证文件完整性
if [ -s "$LOCAL_RULES.new" ]; then
    mv "$LOCAL_RULES.new" "$LOCAL_RULES"
    echo "✅ AI助手规则已更新到最新版本"
else
    echo "❌ 更新失败，保持当前版本"
    rm -f "$LOCAL_RULES.new"
fi
```

### 版本控制策略
```yaml
# .github/workflows/rules-update.yml
name: AI Rules Update
on:
  schedule:
    - cron: '0 9 * * 1' # 每周一上午9点检查更新
  workflow_dispatch:

jobs:
  update-rules:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Check for updates
        run: |
          # 检查规则文件更新
          ./scripts/check-rules-update.sh
      - name: Create PR if updated
        uses: peter-evans/create-pull-request@v5
        with:
          title: "🤖 AI助手规则自动更新"
          body: "自动检测到AI助手规则文件有更新，请审查后合并。"
```

### 性能监控
```typescript
// 规则执行性能监控
class RulesPerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();

  async measureRuleExecution<T>(
    ruleName: string,
    execution: () => Promise<T>
  ): Promise<T> {
    const startTime = performance.now();

    try {
      const result = await execution();
      const endTime = performance.now();

      this.recordMetric(ruleName, {
        duration: endTime - startTime,
        success: true,
        timestamp: new Date()
      });

      return result;
    } catch (error) {
      this.recordMetric(ruleName, {
        duration: performance.now() - startTime,
        success: false,
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }
}
```

---

## 📞 支持与社区

### 问题报告模板
```markdown
## 🐛 问题报告

### 环境信息
- AI助手: [Claude Code/Cursor/VS Code]
- 规则版本: [版本号]
- 操作系统: [macOS/Windows/Linux]
- Node.js版本: [版本号]

### 问题描述
[详细描述遇到的问题]

### 重现步骤
1. [步骤1]
2. [步骤2]
3. [步骤3]

### 期望行为
[描述期望的正确行为]

### 实际行为
[描述实际发生的行为]

### 相关日志
```
[粘贴相关的错误日志或输出]
```

### 附加信息
[任何其他有助于解决问题的信息]
```

### 功能请求模板
```markdown
## 💡 功能请求

### 功能描述
[清晰描述建议的功能]

### 使用场景
[描述什么情况下会使用这个功能]

### 预期收益
[这个功能将如何改善开发体验]

### 实现建议
[如果有实现想法，请描述]

### 替代方案
[是否考虑过其他解决方案]
```

---

*此规则文件基于2025年最新的AI助手技术和开发最佳实践制定，旨在提供智能、高效、安全的开发协助体验。规则文件支持持续更新和个性化定制，以适应不断发展的技术生态系统。*

**版本**: 2.0.0 | **最后更新**: 2025-08-13 | **许可证**: MIT
