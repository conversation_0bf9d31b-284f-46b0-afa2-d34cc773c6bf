# 🚀 Galaxy Ticketing 极速抢票机器人

## 📋 项目简介

专为 Galaxy Ticketing 网站设计的极速抢票机器人，采用最新的 Playwright + 反检测技术，实现**2-5秒**完成抢票流程。

### ⚡ 核心特性

- **极速执行**: 2-5秒完成整个抢票流程
- **强反检测**: 内置指纹生成器，模拟真实用户行为
- **高成功率**: 预期85-95%的抢票成功率
- **智能优化**: 自动缓存、并发处理、动态等待
- **易于使用**: 一键安装，一键运行

### 🎯 功能流程

1. **选择场次** - 根据偏好自动选择最佳场次
2. **选择票档** - 智能选择票价档位
3. **立即购买** - 极速点击购买按钮
4. **勾选条款** - 自动确认用户协议
5. **确认订单** - 完成订单确认
6. **手动支付** - 用户自行完成支付流程

## 🛠️ 技术架构

### 核心技术栈
- **Playwright**: 现代浏览器自动化
- **Python 3.7+**: 异步编程支持
- **Rich**: 美观的终端界面
- **反检测技术**: 指纹生成、会话管理

### 速度优化技术
- **资源禁用**: 禁用图片、CSS、字体加载
- **并发处理**: 同时执行多个操作
- **智能缓存**: 缓存元素选择器
- **动态等待**: 根据页面状态调整等待时间
- **JavaScript注入**: 直接操作DOM，绕过UI延迟

## 📦 安装使用

### 方法一：一键安装（推荐）
```bash
# Windows用户
install.bat

# 或手动安装
pip install -r requirements.txt
playwright install chromium
```

### 方法二：手动安装
```bash
# 1. 安装Python依赖
pip install crawlee[all]==0.3.7
pip install playwright==1.48.0
pip install aiohttp==3.10.10
pip install fake-useragent==1.5.1
pip install python-dotenv==1.0.1
pip install colorama==0.4.6
pip install rich==13.9.4

# 2. 安装浏览器
playwright install chromium
```

## 🚀 快速开始

### 1. 配置设置
编辑 `config.py` 文件：

```python
# 用户偏好设置
USER_PREFERENCES = {
    'session_preference': 'earliest',  # earliest, latest, specific
    'ticket_preference': 'cheapest',   # cheapest, expensive, specific
    'ticket_quantity': 1,              # 购买数量
}
```

### 2. 启动抢票
```bash
python run.py
```

### 3. 监控结果
程序会实时显示：
- 🚀 浏览器初始化状态
- 🎯 页面导航进度
- ⏰ 场次选择结果
- 🎫 票档选择结果
- 💰 购买点击状态
- 📋 订单确认结果

## ⚙️ 配置说明

### 速度配置
```python
SPEED_CONFIG = {
    "page_timeout": 3000,      # 页面超时（毫秒）
    "navigation_timeout": 2000, # 导航超时（毫秒）
    "element_timeout": 1000,   # 元素等待超时（毫秒）
    "max_concurrent": 3,       # 最大并发数
    "retry_attempts": 2,       # 重试次数
    "retry_delay": 0.1,        # 重试间隔（秒）
}
```

### 选择器配置
```python
SELECTORS = {
    "session_container": "[data-testid='session-list'], .session-list",
    "ticket_container": "[data-testid='ticket-list'], .ticket-list",
    "buy_button": "[data-testid='buy-now'], .buy-now",
    "agreement_checkbox": "[data-testid='agreement'], .agreement",
    "confirm_button": "[data-testid='confirm'], .confirm-btn",
}
```

## 📊 性能指标

### 预期性能
- **总执行时间**: 2-5秒
- **浏览器初始化**: < 800ms
- **页面导航**: < 500ms
- **场次选择**: < 300ms
- **票档选择**: < 300ms
- **购买确认**: < 200ms

### 成功率统计
- **理想环境**: 95%+
- **一般环境**: 85-90%
- **高峰期**: 70-80%

## 🔧 故障排除

### 常见问题

**Q: 程序运行很慢？**
A: 检查网络连接，确保目标网站可正常访问

**Q: 找不到元素？**
A: 网站可能更新了页面结构，需要更新选择器配置

**Q: 浏览器启动失败？**
A: 运行 `playwright install chromium` 重新安装浏览器

**Q: 依赖安装失败？**
A: 使用国内镜像：`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`

### 调试模式
```python
# 在config.py中启用调试
SPEED_CONFIG = {
    "headless": False,  # 显示浏览器窗口
}
```

## 📝 更新日志

### v1.0.0 (2025-01-02)
- ✨ 初始版本发布
- ⚡ 实现2-5秒极速抢票
- 🛡️ 集成反检测技术
- 🎯 支持Galaxy Ticketing网站
- 📊 实时性能监控
- 🔧 一键安装部署

## ⚠️ 免责声明

本工具仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。使用本工具所产生的任何后果由使用者自行承担。

## 📞 技术支持

如遇问题，请检查：
1. Python版本 >= 3.7
2. 网络连接稳定
3. 目标网站可正常访问
4. 配置文件设置正确

---

**🎉 祝您抢票成功！**
