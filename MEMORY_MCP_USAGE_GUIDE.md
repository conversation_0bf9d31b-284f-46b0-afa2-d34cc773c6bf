# 🧠 Memory-Enabled MCP Usage Guide

> **Setup Date**: 2025-08-13  
> **Status**: Successfully Configured  
> **Memory Servers**: 3 Active

---

## ✅ **Successfully Installed Memory MCP Servers**

### 1. **Memory Keeper** (v0.10.1)
- **Package**: `mcp-memory-keeper`
- **Function**: Persistent context management for Claude Code
- **Storage**: `C:\Users\<USER>\claude-memory`
- **Features**: 
  - Context preparation and compaction
  - Session state management
  - Memory organization

### 2. **Official Memory Server** (v2025.8.4)
- **Package**: `@modelcontextprotocol/server-memory`
- **Function**: Knowledge graph-based memory
- **Storage**: `C:\Users\<USER>\claude-memory\knowledge-graph`
- **Features**:
  - Entity relationship mapping
  - Semantic memory storage
  - Graph-based retrieval

### 3. **Advanced Memory Bank** (v8.0.5)
- **Package**: `@andrebuzeli/advanced-memory-bank-mcp`
- **Function**: Enhanced memory with reasoning engine
- **Storage**: `C:\Users\<USER>\claude-memory\memory-bank`
- **Features**:
  - Unified tools and reasoning
  - Enhanced planning capabilities
  - MCP interoperability

---

## 🚀 **How to Use Memory Features**

### Starting a Persistent Conversation

1. **Restart Claude Desktop** (important!)
2. **Start a new conversation**
3. **Test memory storage**:
   ```
   "Remember that I prefer TypeScript over JavaScript for all projects"
   ```

4. **Verify memory works**:
   ```
   "What programming language do I prefer?"
   ```

### Memory Commands You Can Use

#### Basic Memory Operations
- `"Remember that [information]"` - Store important information
- `"What do you remember about [topic]?"` - Retrieve stored information
- `"Forget about [topic]"` - Remove specific memories
- `"Show me my memory summary"` - View stored information

#### Advanced Memory Features
- `"Create a knowledge graph about [project]"` - Build relationship maps
- `"Connect [concept A] with [concept B]"` - Create relationships
- `"Prepare context compaction"` - Optimize memory usage
- `"Show memory bank status"` - View memory organization

### Project-Specific Memory
- `"Remember this project uses React with TypeScript"`
- `"Store the API endpoint: https://api.example.com"`
- `"Remember the database schema: [details]"`
- `"What do you know about this project?"`

---

## 💡 **Best Practices**

### 1. **Effective Memory Usage**
- Be specific when storing information
- Use clear, descriptive language
- Organize information by project or topic
- Regularly review and update stored memories

### 2. **Memory Organization**
```
"Remember for project [ProjectName]: 
- Tech stack: React, TypeScript, Node.js
- Database: PostgreSQL
- API style: REST
- Deployment: Docker on AWS"
```

### 3. **Context Management**
- Use memory to maintain long-term project context
- Store coding preferences and patterns
- Remember team conventions and standards
- Keep track of project decisions and rationale

---

## 🔧 **Troubleshooting**

### If Memory Doesn't Work
1. **Restart Claude Desktop** completely
2. **Check configuration**:
   ```powershell
   Get-Content "$env:APPDATA\Claude\claude_desktop_config.json"
   ```
3. **Verify memory directories exist**:
   ```powershell
   Test-Path "C:\Users\<USER>\claude-memory"
   ```

### If MCP Servers Fail to Load
1. **Check internet connection**
2. **Update npm packages**:
   ```powershell
   npm update -g
   ```
3. **Reinstall MCP servers**:
   ```powershell
   npx mcp-memory-keeper@latest
   ```

### Memory Storage Issues
- **Check disk space** in memory directories
- **Verify write permissions** for memory folders
- **Clear memory cache** if needed:
  ```powershell
  Remove-Item "C:\Users\<USER>\claude-memory\*" -Recurse -Force
  ```

---

## 📊 **Memory Storage Locations**

| Type | Location | Purpose |
|------|----------|---------|
| **Main Memory** | `C:\Users\<USER>\claude-memory` | General memory storage |
| **Knowledge Graph** | `C:\Users\<USER>\claude-memory\knowledge-graph` | Relationship mapping |
| **Memory Bank** | `C:\Users\<USER>\claude-memory\memory-bank` | Advanced reasoning |
| **Configuration** | `C:\Users\<USER>\AppData\Roaming\Claude\claude_desktop_config.json` | MCP settings |

---

## 🎯 **Testing Your Setup**

### Test 1: Basic Memory
```
You: "Remember that I work primarily with Python and prefer FastAPI for web APIs"
Claude: [Should acknowledge and store this information]

You: "What web framework do I prefer for Python?"
Claude: [Should recall FastAPI preference]
```

### Test 2: Project Memory
```
You: "Remember for project 'WebApp': Uses React frontend, Node.js backend, PostgreSQL database"
Claude: [Should store project-specific information]

You: "What database does the WebApp project use?"
Claude: [Should recall PostgreSQL]
```

### Test 3: Cross-Session Memory
1. Have the above conversation
2. **Close Claude Desktop completely**
3. **Restart Claude Desktop**
4. **Start a new conversation**
5. Ask: "What do you remember about my preferences?"
6. Claude should recall your stored information

---

## 🎉 **Success Indicators**

✅ **Memory is working if**:
- Claude remembers information across conversations
- You can ask "What do you remember about X?" and get relevant responses
- Project-specific information is retained
- Preferences and patterns are recalled accurately

✅ **Advanced features working if**:
- Knowledge graphs are created and maintained
- Complex relationships between concepts are understood
- Context compaction works when hitting limits
- Memory organization improves over time

---

**🎊 Congratulations! You now have a Claude with persistent memory that can maintain context across conversations and remember important information indefinitely!**

*Last updated: 2025-08-13 | Memory servers: 3 active | Status: Ready for use*
