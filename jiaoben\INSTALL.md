# 🚀 Galaxy Ticket Bot - 安装和运行指南

## 📋 系统要求

- **Node.js** >= 18.0.0
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: 建议 4GB 以上
- **硬盘**: 至少 2GB 可用空间

## 🔧 安装步骤

### 1. 检查 Node.js 版本
```bash
node --version
npm --version
```

如果未安装 Node.js，请访问 [nodejs.org](https://nodejs.org/) 下载安装。

### 2. 安装项目依赖

**方法一：使用 npm (推荐)**
```bash
cd jiaoben
npm install
```

**方法二：使用 yarn**
```bash
cd jiaoben
yarn install
```

**方法三：使用 pnpm**
```bash
cd jiaoben
pnpm install
```

### 3. 安装浏览器 (必需)
```bash
# 安装 Chromium 浏览器
npm run install-browsers

# 或手动安装
npx playwright install chromium
```

### 4. 配置设置

**复制配置文件：**
```bash
# 配置文件已创建为 .env
# 请直接编辑 .env 文件
```

**编辑配置文件：**
```bash
# Windows
notepad .env

# macOS
open -e .env

# Linux
nano .env
```

## ⚙️ 配置说明

### 必需配置项

```env
# 目标网站 (必需)
TARGET_URL=https://www.galaxyticketing.com

# 用户信息 (建议预先配置)
USER_NAME=您的姓名
USER_EMAIL=您的邮箱@example.com
USER_PHONE=您的电话号码

# 代理设置 (可选但推荐)
PROXY_SERVER=您的代理地址:端口
PROXY_USERNAME=代理用户名
PROXY_PASSWORD=代理密码
```

### 反检测配置

```env
# 指纹伪装 (推荐启用)
ENABLE_FINGERPRINT=true
ENABLE_STEALTH=true

# 设备类型
DEVICE_TYPE=desktop
OS_TYPE=windows
BROWSER_LOCALE=zh-CN,zh,en-US,en
```

### 高级配置

```env
# 浏览器设置
HEADLESS=false              # 建议设为 false 便于观察
BROWSER_TYPE=chromium       # chromium/firefox/webkit
MAX_RETRIES=3               # 重试次数

# 性能设置
PAGE_LOAD_WAIT=2000         # 页面加载等待时间
ELEMENT_TIMEOUT=10000       # 元素查找超时
SAVE_SCREENSHOTS=true       # 保存截图
```

## 🚀 启动程序

### 方法一：使用 npm 脚本 (推荐)
```bash
npm start
```

### 方法二：直接运行
```bash
node src/index.js
```

### 方法三：开发模式
```bash
npm run dev
```

## 📱 使用界面

启动后会看到交互式菜单：

```
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🎫 Galaxy Ticket Bot - 混合优化方案                        ║
║                                                              ║
║    🛡️  三重反检测保护 (Fingerprint + Stealth + 自定义)        ║
║    ⚡  智能重试机制                                           ║
║    🎭  动态身份切换                                           ║
║    📊  实时状态监控                                           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

? 请选择操作: (Use arrow keys)
❯ 🚀 开始购票
  ⚙️  配置设置
  📊 系统状态
  🧪 测试连接
  📖 使用帮助
  ❌ 退出程序
```

## 🎯 购票流程

### 1. 选择"🚀 开始购票"
程序会提示您输入购票信息（如果配置文件中未设置）

### 2. 自动购票流程
- **🌐 访问网站** - 使用反检测技术访问目标网站
- **🔍 分析页面** - 智能识别页面结构
- **🎭 选择活动** - 根据配置选择目标活动
- **🎫 选择票务** - 设置票数和类型
- **📝 填写信息** - 自动填写用户信息
- **✅ 确认订单** - 提交订单

### 3. 手动支付
程序会停在支付页面，**您需要手动完成支付流程**

## 📊 监控和日志

### 实时监控
- 购票进度实时显示
- 浏览器状态监控
- 网络请求追踪

### 日志文件
```
logs/
├── combined.log    # 完整日志
└── error.log       # 错误日志

screenshots/
├── 01-homepage.png
├── 02-event-selected.png
├── 03-tickets-selected.png
├── 04-info-filled.png
└── 05-order-confirmed.png
```

## 🔧 故障排除

### 常见问题

**1. 依赖安装失败**
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

**2. 浏览器启动失败**
```bash
# 重新安装浏览器
npx playwright install chromium --force
```

**3. 权限错误 (Linux/macOS)**
```bash
# 给予执行权限
chmod +x src/index.js
```

**4. 端口占用**
```bash
# 检查端口占用
netstat -ano | findstr :3000  # Windows
lsof -i :3000                 # macOS/Linux
```

### 调试模式

**启用详细日志：**
```env
LOG_LEVEL=debug
```

**保存更多截图：**
```env
SAVE_SCREENSHOTS=true
```

**使用开发者工具：**
```env
HEADLESS=false
```

## 🛡️ 安全建议

### 代理使用
- 建议使用付费代理服务
- 选择地理位置接近的代理节点
- 定期更换代理IP

### 配置安全
- 不要在公共场所使用
- 定期更新依赖包
- 保护好配置文件中的敏感信息

### 使用规范
- 遵守目标网站的使用条款
- 不要过度频繁使用
- 仅用于合法购票目的

## 📞 技术支持

### 获取帮助
1. 查看程序内置帮助：选择"📖 使用帮助"
2. 检查日志文件：`logs/error.log`
3. 使用测试功能：选择"🧪 测试连接"

### 更新维护
```bash
# 更新依赖
npm update

# 更新浏览器
npx playwright install chromium
```

---

## ✅ 安装检查清单

- [ ] Node.js >= 18.0.0 已安装
- [ ] 项目依赖已安装 (`npm install`)
- [ ] 浏览器已安装 (`npm run install-browsers`)
- [ ] 配置文件已设置 (`.env`)
- [ ] 用户信息已配置
- [ ] 代理已配置 (可选)
- [ ] 程序可正常启动 (`npm start`)

**🎉 完成以上步骤后，您就可以开始使用 Galaxy Ticket Bot 了！**
