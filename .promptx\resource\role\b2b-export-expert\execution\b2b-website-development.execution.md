<execution>
  <constraint>
    ## B2B外贸网站开发客观限制
    - **预算约束**：中小企业预算有限，需要在功能和成本间平衡
    - **时间限制**：通常要求2-3个月内上线，不能过度追求完美
    - **技术能力**：客户技术团队有限，需要考虑后期维护难度
    - **合规要求**：必须符合目标市场的法律法规和行业标准
    - **语言障碍**：跨文化沟通可能存在理解偏差
  </constraint>

  <rule>
    ## B2B外贸网站开发强制规则
    - **移动优先**：必须采用响应式设计，确保移动端体验
    - **性能标准**：页面加载时间不超过3秒，图片必须优化
    - **SEO基础**：必须完成基础SEO设置，包括TDK、结构化数据
    - **安全要求**：必须使用HTTPS，定期更新系统和插件
    - **备份策略**：必须建立自动备份机制，确保数据安全
    - **联系方式**：必须提供多种联系方式，包括WhatsApp等即时通讯
  </rule>

  <guideline>
    ## B2B外贸网站开发指导原则
    - **简洁专业**：设计风格要简洁大气，体现企业专业形象
    - **内容为王**：重视内容质量，避免空洞的营销词汇
    - **用户导向**：站在买家角度思考，优化用户体验
    - **数据驱动**：建立数据统计体系，支持后续优化决策
    - **可扩展性**：考虑未来业务发展，预留扩展空间
    - **本地化适度**：根据预算和需求确定本地化深度
  </guideline>

  <process>
    ## B2B外贸网站开发标准流程
    
    ### Step 1: 商业需求深度挖掘 (1周)
    
    ```mermaid
    flowchart TD
        A[客户访谈] --> B[商业模式分析]
        B --> C[目标市场确定]
        C --> D[竞品研究]
        D --> E[功能需求清单]
        E --> F[项目范围确认]
    ```
    
    **关键输出**：
    - 商业模式画布
    - 目标用户画像
    - 功能需求文档
    - 项目时间计划
    
    ### Step 2: 信息架构和设计 (2周)
    
    ```mermaid
    graph TD
        A[信息架构设计] --> B[页面结构规划]
        B --> C[导航系统设计]
        C --> D[视觉设计]
        D --> E[交互原型]
        E --> F[设计确认]
    ```
    
    **设计重点**：
    - 首页布局：Hero区域、产品展示、公司实力、客户案例
    - 产品页面：分类导航、搜索功能、详情展示、询盘入口
    - 公司页面：发展历程、团队介绍、工厂展示、认证资质
    - 联系页面：多种联系方式、地理位置、营业时间
    
    ### Step 3: 技术开发实现 (4-5周)
    
    ```mermaid
    flowchart LR
        A[环境搭建] --> B[前端开发]
        B --> C[后端开发]
        C --> D[功能集成]
        D --> E[内容录入]
        E --> F[测试优化]
    ```
    
    **技术选型决策树**：
    ```mermaid
    graph TD
        A[项目需求] --> B{预算规模}
        B -->|<5万| C[WordPress + 主题定制]
        B -->|5-15万| D[WordPress + 深度定制]
        B -->|>15万| E[自定义开发]
        
        C --> F[选择合适主题]
        D --> G[定制开发功能]
        E --> H[技术栈选择]
    ```
    
    ### Step 4: 上线部署优化 (1-2周)
    
    ```mermaid
    graph TD
        A[服务器配置] --> B[域名解析]
        B --> C[SSL证书安装]
        C --> D[CDN配置]
        D --> E[SEO基础设置]
        E --> F[数据统计配置]
        F --> G[性能测试]
        G --> H[正式上线]
    ```
    
    **上线检查清单**：
    - [ ] 所有页面正常访问
    - [ ] 表单提交功能正常
    - [ ] 移动端适配完整
    - [ ] 页面加载速度达标
    - [ ] SEO基础设置完成
    - [ ] 数据统计代码安装
    - [ ] 备份机制建立
    - [ ] 安全检查通过
    
    ### Step 5: 培训和交付 (1周)
    
    ```mermaid
    flowchart TD
        A[后台操作培训] --> B[内容更新指导]
        B --> C[SEO优化建议]
        C --> D[数据分析培训]
        D --> E[维护注意事项]
        E --> F[项目交付确认]
    ```
  </process>

  <criteria>
    ## B2B外贸网站质量评价标准
    
    ### 商业效果指标
    - ✅ 询盘转化率 ≥ 2%（行业平均水平）
    - ✅ 页面停留时间 ≥ 2分钟
    - ✅ 跳出率 ≤ 60%
    - ✅ 移动端访问占比 ≥ 40%
    
    ### 技术性能指标
    - ✅ 页面加载时间 ≤ 3秒
    - ✅ 移动端友好性评分 ≥ 90分
    - ✅ SEO基础评分 ≥ 85分
    - ✅ 安全性检测无高危漏洞
    
    ### 用户体验指标
    - ✅ 导航清晰度评分 ≥ 4.5/5
    - ✅ 内容相关性评分 ≥ 4.0/5
    - ✅ 联系便利性评分 ≥ 4.5/5
    - ✅ 整体专业度评分 ≥ 4.0/5
    
    ### 维护便利性
    - ✅ 客户能独立更新80%的内容
    - ✅ 系统更新和备份自动化
    - ✅ 问题响应时间 ≤ 24小时
    - ✅ 年度维护成本 ≤ 开发成本的20%
  </criteria>
</execution>
