# Install Memory-Enabled MCP Servers
Write-Host "🧠 Installing Memory-Enabled MCP Servers..." -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Create memory directories
$memoryDir = "$env:USERPROFILE\claude-memory"
$knowledgeGraphDir = "$memoryDir\knowledge-graph"
$memoryBankDir = "$memoryDir\memory-bank"

Write-Host "📁 Creating memory directories..." -ForegroundColor Blue
New-Item -ItemType Directory -Path $memoryDir -Force | Out-Null
New-Item -ItemType Directory -Path $knowledgeGraphDir -Force | Out-Null
New-Item -ItemType Directory -Path $memoryBankDir -Force | Out-Null
Write-Host "✅ Memory directories created successfully" -ForegroundColor Green

# Backup existing config
$claudeConfigDir = "$env:APPDATA\Claude"
$claudeConfigFile = "$claudeConfigDir\claude_desktop_config.json"

Write-Host "💾 Backing up existing configuration..." -ForegroundColor Blue
if (Test-Path $claudeConfigFile) {
    $backupFile = "$claudeConfigDir\claude_desktop_config_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    Copy-Item $claudeConfigFile $backupFile
    Write-Host "✅ Configuration backed up to: $backupFile" -ForegroundColor Green
} else {
    Write-Host "ℹ️ No existing configuration found" -ForegroundColor Yellow
}

# Install new configuration
Write-Host "🔧 Installing memory-enabled configuration..." -ForegroundColor Blue
Copy-Item "claude-desktop-final-config.json" $claudeConfigFile -Force
Write-Host "✅ Memory-enabled configuration installed" -ForegroundColor Green

# Verify available MCP servers
Write-Host "🧪 Verifying MCP servers availability..." -ForegroundColor Blue

$mcpServers = @(
    @{Name="Memory Keeper"; Package="mcp-memory-keeper"},
    @{Name="Official Memory Server"; Package="@modelcontextprotocol/server-memory"},
    @{Name="Advanced Memory Bank"; Package="@andrebuzeli/advanced-memory-bank-mcp"}
)

foreach ($server in $mcpServers) {
    Write-Host "  Checking $($server.Name)..." -ForegroundColor Cyan
    $result = npm view $server.Package version 2>$null
    if ($result) {
        Write-Host "    ✅ Available: v$result" -ForegroundColor Green
    } else {
        Write-Host "    ❌ Not available" -ForegroundColor Red
    }
}

# Test installation
Write-Host "🚀 Testing MCP server installation..." -ForegroundColor Blue
Write-Host "  Installing Memory Keeper..." -ForegroundColor Cyan
try {
    $output = npx mcp-memory-keeper --help 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "    ✅ Memory Keeper installed successfully" -ForegroundColor Green
    } else {
        Write-Host "    ⚠️ Memory Keeper installation needs verification" -ForegroundColor Yellow
    }
} catch {
    Write-Host "    ⚠️ Memory Keeper installation test failed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Memory-enabled MCP setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 What you now have:" -ForegroundColor Cyan
Write-Host "  ✅ Memory Keeper - Persistent context management" -ForegroundColor Yellow
Write-Host "  ✅ Official Memory Server - Knowledge graph storage" -ForegroundColor Yellow
Write-Host "  ✅ Advanced Memory Bank - Enhanced memory with reasoning" -ForegroundColor Yellow
Write-Host "  ✅ All existing MCP servers (GitHub, Context7, etc.)" -ForegroundColor Yellow
Write-Host ""
Write-Host "📁 Memory storage locations:" -ForegroundColor Cyan
Write-Host "  - Main memory: $memoryDir" -ForegroundColor Yellow
Write-Host "  - Knowledge graph: $knowledgeGraphDir" -ForegroundColor Yellow
Write-Host "  - Memory bank: $memoryBankDir" -ForegroundColor Yellow
Write-Host ""
Write-Host "🚀 Next steps:" -ForegroundColor Cyan
Write-Host "  1. Restart Claude Desktop application" -ForegroundColor Yellow
Write-Host "  2. Start a new conversation" -ForegroundColor Yellow
Write-Host "  3. Test memory by asking: 'Remember that I prefer TypeScript'" -ForegroundColor Yellow
Write-Host "  4. In a new conversation, ask: 'What do you remember about my preferences?'" -ForegroundColor Yellow
Write-Host ""
Write-Host "💡 Memory features:" -ForegroundColor Cyan
Write-Host "  - Persistent memory across conversations" -ForegroundColor Yellow
Write-Host "  - Knowledge graph for complex relationships" -ForegroundColor Yellow
Write-Host "  - Context management and compression" -ForegroundColor Yellow
Write-Host "  - Automatic memory organization" -ForegroundColor Yellow
