#!/usr/bin/env python3
"""
Galaxy Ticketing 极速抢票启动器
一键启动脚本
"""

import asyncio
import sys
import os
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from galaxy_ticket_bot import GalaxyTicketBot
from config import TARGET_URL, USER_PREFERENCES

console = Console()

def show_banner():
    """显示启动横幅"""
    banner = Text()
    banner.append("🚀 Galaxy Ticketing 极速抢票机器人 🚀\n", style="bold cyan")
    banner.append("版本: 1.0.0 - 极速优化版\n", style="green")
    banner.append("目标: 2-5秒完成抢票流程\n", style="yellow")
    
    console.print(Panel(banner, title="抢票机器人", border_style="cyan"))

def show_config():
    """显示当前配置"""
    config_text = Text()
    config_text.append(f"🎯 目标网站: {TARGET_URL}\n", style="white")
    config_text.append(f"⏰ 场次偏好: {USER_PREFERENCES['session_preference']}\n", style="white")
    config_text.append(f"🎫 票档偏好: {USER_PREFERENCES['ticket_preference']}\n", style="white")
    config_text.append(f"📊 购买数量: {USER_PREFERENCES['ticket_quantity']}\n", style="white")
    
    console.print(Panel(config_text, title="当前配置", border_style="green"))

def show_instructions():
    """显示使用说明"""
    instructions = Text()
    instructions.append("📋 使用说明:\n", style="bold yellow")
    instructions.append("1. 确保网络连接稳定\n", style="white")
    instructions.append("2. 脚本将自动完成: 选择场次 → 选择票档 → 立即购买 → 确认订单\n", style="white")
    instructions.append("3. 支付环节需要您手动完成\n", style="white")
    instructions.append("4. 按 Ctrl+C 可随时停止\n", style="white")
    
    console.print(Panel(instructions, title="操作指南", border_style="yellow"))

async def main():
    """主启动函数"""
    try:
        # 显示启动信息
        show_banner()
        show_config()
        show_instructions()
        
        # 确认启动
        console.print("\n🤖 准备启动抢票机器人...", style="bold cyan")
        
        # 倒计时
        for i in range(3, 0, -1):
            console.print(f"⏳ {i}秒后开始...", style="yellow")
            await asyncio.sleep(1)
        
        console.print("🚀 开始抢票！", style="bold green")
        
        # 启动机器人
        bot = GalaxyTicketBot()
        results = await bot.run_speed_test()
        
        # 显示最终结果
        if results:
            console.print("\n" + "="*50, style="cyan")
            console.print("🎉 抢票任务完成！", style="bold green")
            console.print("="*50, style="cyan")
            
            # 性能统计
            total_time = results.get('total_time', 0)
            if total_time > 0:
                if total_time < 2000:
                    status = "🏆 极速完成！"
                    style = "bold green"
                elif total_time < 5000:
                    status = "⚡ 快速完成！"
                    style = "bold yellow"
                else:
                    status = "✅ 任务完成"
                    style = "bold blue"
                
                console.print(f"\n{status}", style=style)
                console.print(f"总用时: {total_time:.1f}ms", style="white")
                
                # 详细步骤时间
                console.print("\n📊 详细性能报告:", style="bold cyan")
                step_names = {
                    'browser_init': '浏览器初始化',
                    'navigation': '页面导航',
                    'session_select': '场次选择',
                    'ticket_select': '票档选择',
                    'buy_click': '点击购买',
                    'confirm': '确认订单'
                }
                
                for key, name in step_names.items():
                    if key in results:
                        time_ms = results[key]
                        if time_ms < 500:
                            style = "green"
                        elif time_ms < 1000:
                            style = "yellow"
                        else:
                            style = "red"
                        console.print(f"  {name}: {time_ms:.1f}ms", style=style)
            
            console.print("\n💡 提示: 现在可以手动完成支付流程", style="bold blue")
        else:
            console.print("\n❌ 抢票过程中遇到问题，请检查网络或网站状态", style="red")
            
    except KeyboardInterrupt:
        console.print("\n\n⏹️  用户手动停止了抢票程序", style="yellow")
        console.print("👋 感谢使用 Galaxy Ticketing 抢票机器人！", style="cyan")
    except Exception as e:
        console.print(f"\n❌ 程序运行出错: {e}", style="red")
        console.print("🔧 请检查网络连接和配置文件", style="yellow")
    finally:
        console.print("\n🔚 程序结束", style="dim")

if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 7):
        console.print("❌ 需要Python 3.7或更高版本", style="red")
        sys.exit(1)
    
    # 检查依赖
    try:
        import playwright
        import rich
    except ImportError as e:
        console.print(f"❌ 缺少依赖包: {e}", style="red")
        console.print("💡 请运行: pip install -r requirements.txt", style="yellow")
        sys.exit(1)
    
    # 运行主程序
    asyncio.run(main())
