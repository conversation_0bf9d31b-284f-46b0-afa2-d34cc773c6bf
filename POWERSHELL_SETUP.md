# 🔧 PowerShell执行策略设置指南

## 问题说明
Windows PowerShell默认禁止运行未签名的脚本，这是一个安全措施。

## 🚀 解决方案

### 方法一：临时允许执行（推荐）
```powershell
# 以管理员身份打开PowerShell，然后执行：
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或者只针对当前会话：
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
```

### 方法二：直接运行脚本内容
```powershell
# 不修改执行策略，直接运行脚本内容
powershell -ExecutionPolicy Bypass -File ".\load-ai-rules.ps1"
```

### 方法三：手动执行脚本内容
如果以上方法都不行，可以手动复制脚本内容到PowerShell中执行。

## 🛠️ 详细步骤

### 步骤1：检查当前执行策略
```powershell
Get-ExecutionPolicy
```

### 步骤2：设置执行策略（选择一种）

#### 选项A：仅当前用户（推荐）
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 选项B：仅当前会话
```powershell
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
```

#### 选项C：系统级别（需要管理员权限）
```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned
```

### 步骤3：运行脚本
```powershell
.\load-ai-rules.ps1
```

### 步骤4：验证配置
```powershell
.\verify-rules-loading.ps1
```

## 🔒 安全说明

- `RemoteSigned`: 允许本地脚本运行，远程脚本需要签名
- `Bypass`: 临时绕过所有限制（仅当前会话）
- `Restricted`: 默认设置，不允许运行脚本

## 🆘 如果仍然无法执行

### 备用方案：手动配置
我将为您提供手动配置的命令，您可以直接在PowerShell中逐行执行。
