# Verify Memory MCP Setup
Write-Host "🔍 Verifying Memory MCP Setup..." -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Check configuration file
$claudeConfigFile = "$env:APPDATA\Claude\claude_desktop_config.json"
Write-Host "📋 Checking Claude Desktop configuration..." -ForegroundColor Blue

if (Test-Path $claudeConfigFile) {
    Write-Host "✅ Configuration file exists" -ForegroundColor Green
    
    # Check if memory servers are configured
    $config = Get-Content $claudeConfigFile | ConvertFrom-Json
    $memoryServers = @("memory-keeper", "official-memory", "advanced-memory-bank")
    
    foreach ($server in $memoryServers) {
        if ($config.mcpServers.$server) {
            Write-Host "  ✅ $server configured" -ForegroundColor Green
        } else {
            Write-Host "  ❌ $server not found in config" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ Configuration file not found" -ForegroundColor Red
}

# Check memory directories
Write-Host ""
Write-Host "📁 Checking memory directories..." -ForegroundColor Blue
$memoryDirs = @(
    "$env:USERPROFILE\claude-memory",
    "$env:USERPROFILE\claude-memory\knowledge-graph", 
    "$env:USERPROFILE\claude-memory\memory-bank"
)

foreach ($dir in $memoryDirs) {
    if (Test-Path $dir) {
        Write-Host "  ✅ $dir exists" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $dir missing" -ForegroundColor Red
    }
}

# Check MCP server availability
Write-Host ""
Write-Host "🌐 Checking MCP server packages..." -ForegroundColor Blue
$packages = @(
    "mcp-memory-keeper",
    "@modelcontextprotocol/server-memory",
    "@andrebuzeli/advanced-memory-bank-mcp"
)

foreach ($package in $packages) {
    Write-Host "  Checking $package..." -ForegroundColor Cyan
    $version = npm view $package version 2>$null
    if ($version) {
        Write-Host "    ✅ Available: v$version" -ForegroundColor Green
    } else {
        Write-Host "    ❌ Not available" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎯 Setup Summary:" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green
Write-Host "✅ Memory-enabled MCP servers have been configured" -ForegroundColor Yellow
Write-Host "✅ Memory storage directories created" -ForegroundColor Yellow
Write-Host "✅ Configuration backed up and updated" -ForegroundColor Yellow
Write-Host ""
Write-Host "🚀 Ready to test!" -ForegroundColor Cyan
Write-Host "1. Restart Claude Desktop" -ForegroundColor Yellow
Write-Host "2. Start a new conversation" -ForegroundColor Yellow
Write-Host "3. Test memory: 'Remember that I prefer TypeScript over JavaScript'" -ForegroundColor Yellow
Write-Host "4. In a new conversation: 'What programming language do I prefer?'" -ForegroundColor Yellow
