# 🎫 Galaxy Ticket Bot - 混合优化方案

> **高成功率购票脚本** - 专为 Galaxy Ticketing 网站设计的自动化购票工具

## 🌟 核心特性

### 🛡️ **三重反检测保护**
- **Fingerprint Suite** - 业界最高评分 (Trust Score: 10.0) 的开源指纹伪装
- **Playwright Stealth** - 专业反检测插件，绕过常见机器人检测
- **自定义优化** - 针对性的反检测脚本和浏览器特征伪装

### ⚡ **智能化特性**
- **动态指纹生成** - 每次运行使用不同的浏览器指纹
- **智能重试机制** - 网络异常和页面错误自动恢复
- **多浏览器支持** - Chromium/Firefox/WebKit 任选
- **实时状态监控** - 购票进度可视化显示

### 🎯 **购票优化**
- **自适应选择器** - 智能识别网站元素变化
- **多种填写策略** - 适应不同的表单结构
- **截图记录** - 完整的购票过程记录
- **用户友好界面** - 交互式命令行界面

## 📋 技术架构

```
Galaxy Ticket Bot (混合优化方案)
├── 🎭 反检测层
│   ├── Fingerprint Suite (指纹伪装)
│   ├── Playwright Stealth (隐身模式)
│   └── 自定义反检测脚本
├── 🌐 浏览器管理层
│   ├── 多浏览器支持
│   ├── 上下文隔离
│   └── 资源管理
├── 🎫 购票逻辑层
│   ├── 智能元素识别
│   ├── 自适应流程
│   └── 错误处理
└── 📊 监控日志层
    ├── 结构化日志
    ├── 性能监控
    └── 状态追踪
```

## 🚀 快速开始

### 1. 环境要求
- **Node.js** >= 18.0.0
- **操作系统**: Windows/macOS/Linux
- **内存**: 建议 4GB 以上

### 2. 安装依赖
```bash
# 克隆项目
git clone <repository-url>
cd galaxy-ticket-bot

# 安装依赖
npm install

# 安装浏览器
npm run install-browsers
```

### 3. 配置设置
```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

### 4. 启动程序
```bash
# 启动购票机器人
npm start

# 或直接运行
node src/index.js
```

## ⚙️ 配置说明

### 基础配置
```env
# 浏览器设置
HEADLESS=false                    # 是否无头模式
BROWSER_TYPE=chromium             # 浏览器类型
MAX_RETRIES=3                     # 最大重试次数
TIMEOUT=30000                     # 请求超时时间

# 目标网站
TARGET_URL=https://www.galaxyticketing.com
TICKET_QUANTITY=1                 # 购票数量
```

### 代理配置 (用户自己配置)
```env
# 代理服务器 (可选但推荐)
PROXY_SERVER=127.0.0.1:8080      # 代理地址
PROXY_USERNAME=                   # 代理用户名
PROXY_PASSWORD=                   # 代理密码
```

### 反检测配置
```env
# 指纹伪装
ENABLE_FINGERPRINT=true           # 启用指纹伪装
ENABLE_STEALTH=true               # 启用隐身模式
DEVICE_TYPE=desktop               # 设备类型
OS_TYPE=windows                   # 操作系统
BROWSER_LOCALE=zh-CN,zh,en-US,en  # 浏览器语言
```

### 用户信息配置
```env
# 购票信息 (建议预先配置)
USER_NAME=张三                    # 姓名
USER_EMAIL=<EMAIL>      # 邮箱
USER_PHONE=13800138000            # 电话
```

## 🎯 使用流程

### 1. 启动程序
运行 `npm start` 后会看到交互式菜单：

```
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🎫 Galaxy Ticket Bot - 混合优化方案                        ║
║                                                              ║
║    🛡️  三重反检测保护 (Fingerprint + Stealth + 自定义)        ║
║    ⚡  智能重试机制                                           ║
║    🎭  动态身份切换                                           ║
║    📊  实时状态监控                                           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

? 请选择操作: (Use arrow keys)
❯ 🚀 开始购票
  ⚙️  配置设置
  📊 系统状态
  🧪 测试连接
  📖 使用帮助
  ❌ 退出程序
```

### 2. 购票流程
选择"开始购票"后，程序会自动执行以下步骤：

1. **🌐 访问网站** - 使用反检测技术访问目标网站
2. **🔍 分析页面** - 智能识别页面结构和元素
3. **🎭 选择活动** - 根据配置选择目标活动
4. **🎫 选择票务** - 设置票数和票价类型
5. **📝 填写信息** - 自动填写用户信息
6. **✅确认订单** - 提交订单并跳转支付页面

### 3. 手动支付
程序会自动停在支付页面，**用户需要手动完成支付流程**。

## 📊 监控与日志

### 实时状态监控
- 购票进度实时显示
- 浏览器状态监控
- 网络请求追踪
- 错误自动记录

### 日志系统
```
logs/
├── combined.log    # 完整日志
├── error.log       # 错误日志
└── screenshots/    # 截图记录
    ├── 01-homepage.png
    ├── 02-event-selected.png
    ├── 03-tickets-selected.png
    ├── 04-info-filled.png
    └── 05-order-confirmed.png
```

## 🛡️ 反检测技术详解

### 1. Fingerprint Suite
- **真实设备指纹** - 基于真实浏览器数据生成
- **动态Header生成** - 智能生成HTTP请求头
- **设备特征伪装** - 屏幕分辨率、时区、语言等

### 2. Playwright Stealth
- **WebDriver隐藏** - 移除自动化检测标识
- **插件模拟** - 模拟真实浏览器插件
- **权限伪装** - 伪装浏览器权限状态

### 3. 自定义优化
- **JavaScript注入** - 覆盖检测函数
- **行为模拟** - 模拟人类操作模式
- **网络特征** - 优化请求时序和频率

## 🔧 高级配置

### 并发购票
```env
# 多实例并发 (谨慎使用)
CONCURRENT_INSTANCES=1            # 并发实例数
```

### 性能优化
```env
# 页面加载优化
PAGE_LOAD_WAIT=2000              # 页面加载等待时间
ELEMENT_TIMEOUT=10000            # 元素查找超时
SAVE_SCREENSHOTS=true           # 保存截图
```

### 监控设置
```env
# 实时监控
ENABLE_MONITORING=true           # 启用监控
MONITORING_INTERVAL=5000         # 监控间隔
LOG_LEVEL=info                   # 日志级别
```

## ⚠️ 重要提醒

### 使用须知
1. **遵守法律法规** - 仅用于合法购票，不得用于黄牛等违法行为
2. **尊重网站规则** - 遵守目标网站的使用条款和限制
3. **适度使用** - 避免过度频繁的请求影响网站正常运行
4. **手动支付** - 支付环节必须手动完成，确保安全

### 技术限制
1. **验证码处理** - 复杂验证码需要手动处理
2. **网站更新** - 网站结构变化可能需要调整选择器
3. **反检测升级** - 网站反检测技术升级可能影响成功率

### 故障排除
1. **浏览器启动失败** - 检查系统权限和防火墙设置
2. **元素找不到** - 网站可能更新了页面结构
3. **代理连接失败** - 检查代理服务器配置和网络连接
4. **购票失败** - 可能是库存不足或网站限制

## 📈 性能优化建议

### 1. 网络优化
- 使用稳定的代理服务器
- 选择地理位置接近的代理节点
- 配置合适的超时时间

### 2. 系统优化
- 关闭不必要的后台程序
- 确保足够的内存空间
- 使用SSD硬盘提升I/O性能

### 3. 配置优化
- 根据网站特点调整等待时间
- 合理设置重试次数
- 启用截图记录便于调试

## 🤝 技术支持

### 常见问题
- 查看 `logs/error.log` 了解错误详情
- 使用 `🧪 测试连接` 功能检查网络状态
- 检查 `.env` 配置文件是否正确

### 更新维护
- 定期更新依赖包版本
- 关注目标网站的结构变化
- 根据需要调整反检测策略

---

## 📄 许可证

MIT License - 仅供学习和合法使用

## 🙏 致谢

感谢以下开源项目的支持：
- [Playwright](https://playwright.dev/) - 现代化的浏览器自动化框架
- [Fingerprint Suite](https://github.com/apify/fingerprint-suite) - 专业的指纹伪装工具
- [Playwright Stealth](https://github.com/berstend/puppeteer-extra/tree/master/packages/puppeteer-extra-plugin-stealth) - 反检测插件

---

**⚡ 开始您的高成功率购票之旅！**
