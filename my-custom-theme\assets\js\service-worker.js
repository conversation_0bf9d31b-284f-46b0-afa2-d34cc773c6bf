/**
 * {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
 * Service Worker for Progressive Web App
 * Provides offline support, cache management and push notifications
 */

const CACHE_NAME = 'timing-belts-v1.0.0';
const STATIC_CACHE = 'static-v1.0.0';
const DYNAMIC_CACHE = 'dynamic-v1.0.0';

// {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
// Static assets that need to be cached
// Use only assets that actually exist in the theme. Note: paths are relative to site origin
const STATIC_ASSETS = [
    '/',
    '/wp-content/themes/my-custom-theme/assets/css/style.css',
    '/wp-content/themes/my-custom-theme/assets/js/modern-core.js',
    '/wp-content/themes/my-custom-theme/assets/js/lazy-loading.js',
    '/wp-content/themes/my-custom-theme/assets/images/timing-belts.png'
];

// {{ AURA-X: Modify - 英文化注释. Confirmed via 寸止 }}
// API endpoints that need to be cached
const API_ENDPOINTS = [
    '/wp-json/wp/v2/posts',
    '/wp-admin/admin-ajax.php'
];

// 安装事件 - 预缓存静态资源
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('Service Worker: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Service Worker: Static assets cached');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Failed to cache static assets', error);
            })
    );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

// 拦截网络请求
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 跳过非GET请求
    if (request.method !== 'GET') {
        return;
    }
    
    // 跳过Chrome扩展请求
    if (url.protocol === 'chrome-extension:') {
        return;
    }
    
    // 处理不同类型的请求
    if (isStaticAsset(request)) {
        event.respondWith(handleStaticAsset(request));
    } else if (isAPIRequest(request)) {
        event.respondWith(handleAPIRequest(request));
    } else if (isPageRequest(request)) {
        event.respondWith(handlePageRequest(request));
    } else {
        event.respondWith(handleOtherRequest(request));
    }
});

// 判断是否为静态资源
function isStaticAsset(request) {
    const url = new URL(request.url);
    return url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|ico)$/);
}

// 判断是否为API请求
function isAPIRequest(request) {
    const url = new URL(request.url);
    return url.pathname.includes('/wp-json/') || 
           url.pathname.includes('/wp-admin/admin-ajax.php');
}

// 判断是否为页面请求
function isPageRequest(request) {
    return request.headers.get('accept').includes('text/html');
}

// 处理静态资源 - Cache First策略
async function handleStaticAsset(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Service Worker: Failed to handle static asset', error);
        return new Response('Asset not available offline', { status: 503 });
    }
}

// 处理API请求 - Network First策略
async function handleAPIRequest(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.log('Service Worker: Network failed, trying cache for API request');
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        return new Response(JSON.stringify({
            error: 'API not available offline',
            offline: true
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// 处理页面请求 - Network First with offline fallback
async function handlePageRequest(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.log('Service Worker: Network failed, trying cache for page request');
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // 返回离线页面
        return new Response('Page not available offline', { 
            status: 503,
            headers: { 'Content-Type': 'text/html' }
        });
    }
}

// 处理其他请求
async function handleOtherRequest(request) {
    try {
        return await fetch(request);
    } catch (error) {
        const cachedResponse = await caches.match(request);
        return cachedResponse || new Response('Resource not available offline', { status: 503 });
    }
}

// 推送通知处理
self.addEventListener('push', (event) => {
    if (!event.data) {
        return;
    }
    
    const data = event.data.json();
    const options = {
        body: data.body || 'New update available',
        icon: '/wp-content/themes/my-custom-theme/assets/images/timing-belts.png',
        badge: '/wp-content/themes/my-custom-theme/assets/images/timing-belts.png',
        tag: data.tag || 'general',
        data: data.data || {},
        actions: [
            {
                action: 'view',
                title: 'View',
                icon: '/assets/images/view-icon.png'
            },
            {
                action: 'dismiss',
                title: 'Dismiss',
                icon: '/assets/images/dismiss-icon.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification(data.title || 'Timing Belts Update', options)
    );
});

// 通知点击处理
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    if (event.action === 'view') {
        const url = event.notification.data.url || '/';
        event.waitUntil(
            clients.openWindow(url)
        );
    }
});

// 后台同步
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    try {
        // 同步离线时的表单提交
        const cache = await caches.open(DYNAMIC_CACHE);
        const requests = await cache.keys();
        
        for (const request of requests) {
            if (request.method === 'POST') {
                try {
                    await fetch(request);
                    await cache.delete(request);
                } catch (error) {
                    console.log('Background sync failed for request:', request.url);
                }
            }
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// 缓存管理
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_URLS') {
        event.waitUntil(
            caches.open(DYNAMIC_CACHE)
                .then(cache => cache.addAll(event.data.urls))
        );
    }
    
    if (event.data && event.data.type === 'CLEAR_CACHE') {
        event.waitUntil(
            caches.keys()
                .then(cacheNames => Promise.all(
                    cacheNames.map(cacheName => caches.delete(cacheName))
                ))
        );
    }
});

// 错误处理
self.addEventListener('error', (event) => {
    console.error('Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Service Worker unhandled rejection:', event.reason);
    event.preventDefault();
});
