// {{ AURA-X: Modify - 超强化导航栏固定功能. Source: context7-mcp on 'CSS sticky navigation' }}
// 导航栏下拉菜单JavaScript - 多重保护机制
function initNavbar() {
    // 获取导航栏元素 - 多种选择器
    let navbar = document.querySelector('.main-navbar') ||
                 document.querySelector('nav.main-navbar') ||
                 document.querySelector('[class*="main-navbar"]') ||
                 document.querySelector('nav');

    if (!navbar) {
        console.warn('Navbar not found, creating fallback');
        return;
    }

    // {{ AURA-X: Modify - 简化导航栏实现，使用CSS Sticky. Source: context7-mcp on 'CSS sticky navigation' }}
    // 移除复杂的JavaScript强制定位逻辑
    // 改用纯CSS sticky方案，更简洁可靠

    // 移除定时检查 - 避免性能问题

    // 移除MutationObserver - 避免无限循环导致的加载问题

    // 移除滚动时的强制检查 - 避免性能问题

    // {{ AURA-X: Modify - 强化固定定位实现. Source: context7-mcp on 'CSS sticky navigation navbar fixed position' }}
    // 启用稳健的固定定位方案，并为 body 预留等高内边距
    function applyFixedNavbar() {
        // 确保导航栏已渲染完成再计算高度
        requestAnimationFrame(() => {
            const height = navbar.offsetHeight || 75;
            // 设置CSS变量，供所有媒体查询使用
            document.documentElement.style.setProperty('--navbar-height', height + 'px');
            document.body.style.setProperty('--navbar-height', height + 'px');
            document.body.classList.add('has-fixed-navbar');
            navbar.classList.add('is-fixed');

            // 强制应用固定定位样式
            navbar.style.position = 'fixed';
            navbar.style.top = '0';
            navbar.style.left = '0';
            navbar.style.right = '0';
            navbar.style.zIndex = '1000';

            console.log('Navbar fixed positioning applied, height:', height + 'px');
        });
    }

    function updateNavbar() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }

    // {{ AURA-X: Modify - 优化初始化和事件监听. Source: context7-mcp on 'CSS sticky navigation navbar fixed position' }}
    // 初始启用 fixed 兜底，并监听窗口尺寸变化自适应高度
    applyFixedNavbar();

    // 防抖处理窗口大小变化
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            // 重新计算高度，避免移动端地址栏变化导致遮挡
            const height = navbar.offsetHeight || 75;
            document.documentElement.style.setProperty('--navbar-height', height + 'px');
            document.body.style.setProperty('--navbar-height', height + 'px');
            console.log('Navbar height updated on resize:', height + 'px');
        }, 100);
    });

    window.addEventListener('scroll', updateNavbar, { passive: true });

    // 获取所有下拉菜单项
    const dropdowns = document.querySelectorAll('.nav-dropdown');
    
    dropdowns.forEach(function(dropdown) {
        const submenu = dropdown.querySelector('.nav-submenu');
        
        if (submenu) {
            // 鼠标进入时显示下拉菜单
            dropdown.addEventListener('mouseenter', function() {
                submenu.style.opacity = '1';
                submenu.style.visibility = 'visible';
                submenu.style.transform = 'translateY(0)';
            });
            
            // 鼠标离开时隐藏下拉菜单
            dropdown.addEventListener('mouseleave', function() {
                submenu.style.opacity = '0';
                submenu.style.visibility = 'hidden';
                submenu.style.transform = 'translateY(-10px)';
            });
        }
    });
    
    // 点击页面其他地方时隐藏所有下拉菜单
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.nav-dropdown')) {
            dropdowns.forEach(function(dropdown) {
                const submenu = dropdown.querySelector('.nav-submenu');
                if (submenu) {
                    submenu.style.opacity = '0';
                    submenu.style.visibility = 'hidden';
                    submenu.style.transform = 'translateY(-10px)';
                }
            });
        }
    });
}

// 保证无论脚本在何时加载，都能初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initNavbar);
} else {
  initNavbar();
}
