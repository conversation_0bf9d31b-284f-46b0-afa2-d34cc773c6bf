# AI Assistant Rules - .gitignore

# Temporary files
*.tmp
*.temp
*~

# Backup files
*.backup
*.bak
*_backup_*

# Log files
*.log
logs/

# Environment variables (keep sensitive data safe)
.env
.env.local
.env.*.local

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
desktop.ini

# IDE files
.vscode/launch.json
.vscode/tasks.json
.idea/
*.swp
*.swo

# Test directories
test-repo/
github-test/

# PowerShell execution policy files
*.ps1xml

# Temporary PowerShell modules
PSReadLine/

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python cache (if any)
__pycache__/
*.py[cod]
*$py.class

# Compiled files
*.exe
*.dll
*.so
*.dylib
