/**
 * Galaxy Ticket Bot - 浏览器管理模块
 * 负责浏览器实例的创建、管理和销毁
 */

import { chromium, firefox, webkit } from 'playwright';
import { config } from '../config/config.js';
import logger from '../utils/logger.js';
import AntiDetectionManager from './anti-detection.js';

/**
 * 浏览器管理器
 */
export class BrowserManager {
  constructor() {
    this.browser = null;
    this.contexts = new Map();
    this.pages = new Map();
    this.antiDetection = new AntiDetectionManager();
    this.isInitialized = false;
  }

  /**
   * 初始化浏览器
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.browserInfo('正在初始化浏览器...');
      
      // 选择浏览器类型
      const browserType = this.getBrowserType();
      
      // 启动浏览器
      this.browser = await browserType.launch({
        ...config.playwright.launchOptions,
        // 添加额外的启动参数
        args: [
          ...config.playwright.launchOptions.args,
          // 禁用自动化检测
          '--disable-blink-features=AutomationControlled',
          '--disable-features=VizDisplayCompositor',
          // 性能优化
          '--memory-pressure-off',
          '--max_old_space_size=4096',
          // 安全相关
          '--disable-web-security',
          '--disable-features=TranslateUI',
          '--disable-extensions-http-throttling',
          // 修复页面显示问题
          '--window-size=1920,1080',
          '--start-maximized',
          '--disable-dev-shm-usage',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          // 确保完整页面渲染
          '--force-device-scale-factor=1',
          '--disable-gpu-sandbox',
        ],
      });

      this.isInitialized = true;
      logger.browserInfo('浏览器初始化成功', {
        type: config.app.browserType,
        headless: config.app.headless,
        version: await this.getBrowserVersion(),
      });

    } catch (error) {
      logger.browserError('浏览器初始化失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取浏览器类型
   */
  getBrowserType() {
    const browserTypes = {
      chromium,
      firefox,
      webkit,
    };

    const browserType = browserTypes[config.app.browserType];
    if (!browserType) {
      logger.browserError(`不支持的浏览器类型: ${config.app.browserType}`);
      throw new Error(`不支持的浏览器类型: ${config.app.browserType}`);
    }

    return browserType;
  }

  /**
   * 获取浏览器版本
   */
  async getBrowserVersion() {
    try {
      return this.browser.version();
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * 创建新的浏览器上下文
   */
  async createContext(contextId = 'default') {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      logger.browserInfo(`正在创建浏览器上下文: ${contextId}`);

      // 生成新指纹
      const fingerprint = this.antiDetection.generateFingerprint();
      
      // 创建带指纹的上下文
      const context = await this.antiDetection.applyFingerprintToContext(
        this.browser,
        fingerprint
      );

      // 设置上下文事件监听
      this.setupContextListeners(context, contextId);

      // 存储上下文
      this.contexts.set(contextId, context);

      logger.browserInfo(`浏览器上下文创建成功: ${contextId}`, {
        fingerprint: !!fingerprint,
        proxy: !!config.proxy.server,
      });

      return context;
    } catch (error) {
      logger.browserError(`浏览器上下文创建失败: ${contextId}`, { error: error.message });
      throw error;
    }
  }

  /**
   * 设置上下文事件监听
   */
  setupContextListeners(context, contextId) {
    // 监听页面创建
    context.on('page', (page) => {
      logger.browserInfo(`新页面创建: ${contextId}`, { url: page.url() });
    });

    // 监听请求
    context.on('request', (request) => {
      logger.debug(`请求: ${request.method()} ${request.url()}`, {
        contextId,
        method: request.method(),
        url: request.url(),
      });
    });

    // 监听响应
    context.on('response', (response) => {
      if (response.status() >= 400) {
        logger.networkError(`响应错误: ${response.status()} ${response.url()}`, {
          contextId,
          status: response.status(),
          url: response.url(),
        });
      }
    });

    // 监听页面错误
    context.on('pageerror', (error) => {
      logger.browserError(`页面错误: ${contextId}`, { error: error.message });
    });
  }

  /**
   * 创建新页面
   */
  async createPage(contextId = 'default', pageId = null) {
    let context = this.contexts.get(contextId);

    if (!context) {
      context = await this.createContext(contextId);
    }

    try {
      const page = await context.newPage();
      const finalPageId = pageId || `${contextId}_${Date.now()}`;

      // 设置页面视口大小，确保完整显示
      await page.setViewportSize({ width: 1920, height: 1080 });

      // 应用反检测措施
      await this.antiDetection.applyStealth(page);
      await this.antiDetection.applyCustomAntiDetection(page);

      // 设置页面事件监听
      this.setupPageListeners(page, finalPageId);

      // 存储页面
      this.pages.set(finalPageId, page);

      logger.browserInfo(`页面创建成功: ${finalPageId}`, {
        contextId,
        url: page.url(),
      });

      return { page, pageId: finalPageId };
    } catch (error) {
      logger.browserError(`页面创建失败: ${contextId}`, { error: error.message });
      throw error;
    }
  }

  /**
   * 设置页面事件监听
   */
  setupPageListeners(page, pageId) {
    // 监听控制台消息
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        logger.browserError(`控制台错误: ${pageId}`, { message: msg.text() });
      }
    });

    // 监听页面崩溃
    page.on('crash', () => {
      logger.browserError(`页面崩溃: ${pageId}`);
    });

    // 监听对话框
    page.on('dialog', async (dialog) => {
      logger.browserInfo(`对话框出现: ${pageId}`, { 
        type: dialog.type(),
        message: dialog.message(),
      });
      await dialog.accept();
    });

    // 监听下载
    page.on('download', (download) => {
      logger.browserInfo(`文件下载: ${pageId}`, { 
        filename: download.suggestedFilename(),
      });
    });
  }

  /**
   * 获取页面
   */
  getPage(pageId) {
    return this.pages.get(pageId);
  }

  /**
   * 获取上下文
   */
  getContext(contextId) {
    return this.contexts.get(contextId);
  }

  /**
   * 关闭页面
   */
  async closePage(pageId) {
    const page = this.pages.get(pageId);
    if (page) {
      try {
        await page.close();
        this.pages.delete(pageId);
        logger.browserInfo(`页面已关闭: ${pageId}`);
      } catch (error) {
        logger.browserError(`页面关闭失败: ${pageId}`, { error: error.message });
      }
    }
  }

  /**
   * 关闭上下文
   */
  async closeContext(contextId) {
    const context = this.contexts.get(contextId);
    if (context) {
      try {
        // 关闭该上下文的所有页面
        const contextPages = Array.from(this.pages.entries())
          .filter(([pageId]) => pageId.startsWith(contextId))
          .map(([pageId]) => pageId);

        for (const pageId of contextPages) {
          await this.closePage(pageId);
        }

        await context.close();
        this.contexts.delete(contextId);
        logger.browserInfo(`上下文已关闭: ${contextId}`);
      } catch (error) {
        logger.browserError(`上下文关闭失败: ${contextId}`, { error: error.message });
      }
    }
  }

  /**
   * 截图
   */
  async takeScreenshot(pageId, filename = null) {
    const page = this.pages.get(pageId);
    if (!page) {
      throw new Error(`页面不存在: ${pageId}`);
    }

    try {
      const screenshotPath = filename || `screenshots/${pageId}_${Date.now()}.png`;
      await page.screenshot({ 
        path: screenshotPath,
        fullPage: true,
      });
      
      logger.browserInfo(`截图已保存: ${pageId}`, { path: screenshotPath });
      return screenshotPath;
    } catch (error) {
      logger.browserError(`截图失败: ${pageId}`, { error: error.message });
      throw error;
    }
  }

  /**
   * 获取页面性能指标
   */
  async getPerformanceMetrics(pageId) {
    const page = this.pages.get(pageId);
    if (!page) {
      throw new Error(`页面不存在: ${pageId}`);
    }

    try {
      const metrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
          firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
        };
      });

      logger.performance(`页面性能指标: ${pageId}`, 0, metrics);
      return metrics;
    } catch (error) {
      logger.browserError(`获取性能指标失败: ${pageId}`, { error: error.message });
      return null;
    }
  }

  /**
   * 清理所有资源
   */
  async cleanup() {
    try {
      logger.browserInfo('正在清理浏览器资源...');

      // 关闭所有页面
      for (const pageId of this.pages.keys()) {
        await this.closePage(pageId);
      }

      // 关闭所有上下文
      for (const contextId of this.contexts.keys()) {
        await this.closeContext(contextId);
      }

      // 关闭浏览器
      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }

      this.isInitialized = false;
      logger.browserInfo('浏览器资源清理完成');
    } catch (error) {
      logger.browserError('浏览器资源清理失败', { error: error.message });
    }
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      browserConnected: !!this.browser?.isConnected(),
      contextsCount: this.contexts.size,
      pagesCount: this.pages.size,
      contexts: Array.from(this.contexts.keys()),
      pages: Array.from(this.pages.keys()),
    };
  }
}

export default BrowserManager;
