"""
Galaxy Ticketing 极速抢票机器人
专门优化的高速抢票实现
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON>er, BrowserContext, Page
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

from config import *
from speed_optimizer import SpeedOptimizer

console = Console()
logger = logging.getLogger(__name__)

class GalaxyTicketBot:
    """Galaxy Ticketing 极速抢票机器人"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.optimizer = SpeedOptimizer()
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, LOG_CONFIG['level']),
            format=LOG_CONFIG['format'],
            handlers=[
                logging.FileHandler(LOG_CONFIG['file']),
                logging.StreamHandler()
            ]
        )
    
    async def initialize_browser(self) -> bool:
        """初始化浏览器 - 极速配置"""
        try:
            console.print("🚀 启动极速浏览器...", style="cyan")
            self.optimizer.start_timer()
            
            playwright = await async_playwright().start()
            
            # 启动浏览器，使用极速配置
            self.browser = await playwright.chromium.launch(
                headless=SPEED_CONFIG['headless'],
                args=BROWSER_ARGS
            )
            
            # 创建上下文，禁用不必要功能
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent=self.optimizer.ua.random,
                java_script_enabled=True,
                images_enabled=not SPEED_CONFIG['disable_images'],
                bypass_csp=True,
                ignore_https_errors=True,
            )
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 应用页面级优化
            await self.optimizer.optimize_page(self.page)
            
            self.optimizer.log_performance("浏览器初始化")
            return True
            
        except Exception as e:
            console.print(f"❌ 浏览器初始化失败: {e}", style="red")
            return False
    
    async def navigate_to_target(self) -> bool:
        """导航到目标页面"""
        try:
            console.print("🎯 导航到抢票页面...", style="cyan")
            self.optimizer.start_timer()
            
            # 快速导航
            await self.page.goto(
                TARGET_URL, 
                wait_until='domcontentloaded',
                timeout=SPEED_CONFIG['navigation_timeout']
            )
            
            # 等待关键元素加载
            await self.optimizer.smart_wait(
                self.page, 
                "document.readyState === 'complete'", 
                max_wait=1000
            )
            
            self.optimizer.log_performance("页面导航")
            return True
            
        except Exception as e:
            console.print(f"❌ 页面导航失败: {e}", style="red")
            return False
    
    async def select_session(self) -> bool:
        """选择场次 - 极速版本"""
        try:
            console.print("⏰ 选择场次...", style="yellow")
            self.optimizer.start_timer()
            
            # 等待场次容器加载
            session_container = await self.optimizer.fast_wait_for_selector(
                self.page, SELECTORS['session_container']
            )
            
            if not session_container:
                console.print("❌ 未找到场次选择区域", style="red")
                return False
            
            # 获取所有场次
            sessions = await self.page.query_selector_all(SELECTORS['session_item'])
            
            if not sessions:
                console.print("❌ 未找到可选场次", style="red")
                return False
            
            # 根据用户偏好选择场次
            target_session = None
            if USER_PREFERENCES['session_preference'] == 'earliest':
                target_session = sessions[0]
            elif USER_PREFERENCES['session_preference'] == 'latest':
                target_session = sessions[-1]
            elif USER_PREFERENCES['session_preference'] == 'specific':
                idx = USER_PREFERENCES['specific_session_index']
                if idx < len(sessions):
                    target_session = sessions[idx]
            
            if target_session:
                # 极速点击
                await target_session.click(force=True)
                await asyncio.sleep(0.1)  # 短暂等待
                
                self.optimizer.log_performance("场次选择")
                return True
            
            return False
            
        except Exception as e:
            console.print(f"❌ 场次选择失败: {e}", style="red")
            return False
    
    async def select_ticket(self) -> bool:
        """选择票档 - 极速版本"""
        try:
            console.print("🎫 选择票档...", style="yellow")
            self.optimizer.start_timer()
            
            # 等待票档容器加载
            ticket_container = await self.optimizer.fast_wait_for_selector(
                self.page, SELECTORS['ticket_container']
            )
            
            if not ticket_container:
                console.print("❌ 未找到票档选择区域", style="red")
                return False
            
            # 获取所有票档
            tickets = await self.page.query_selector_all(SELECTORS['ticket_item'])
            
            if not tickets:
                console.print("❌ 未找到可选票档", style="red")
                return False
            
            # 根据用户偏好选择票档
            target_ticket = None
            if USER_PREFERENCES['ticket_preference'] == 'cheapest':
                target_ticket = tickets[0]  # 通常最便宜的在前面
            elif USER_PREFERENCES['ticket_preference'] == 'expensive':
                target_ticket = tickets[-1]  # 通常最贵的在后面
            elif USER_PREFERENCES['ticket_preference'] == 'specific':
                idx = USER_PREFERENCES['specific_ticket_index']
                if idx < len(tickets):
                    target_ticket = tickets[idx]
            
            if target_ticket:
                # 极速点击
                await target_ticket.click(force=True)
                await asyncio.sleep(0.1)  # 短暂等待
                
                self.optimizer.log_performance("票档选择")
                return True
            
            return False
            
        except Exception as e:
            console.print(f"❌ 票档选择失败: {e}", style="red")
            return False
    
    async def click_buy_now(self) -> bool:
        """点击立即购买"""
        try:
            console.print("💰 点击立即购买...", style="yellow")
            self.optimizer.start_timer()
            
            # 极速点击购买按钮
            success = await self.optimizer.fast_click(self.page, SELECTORS['buy_button'])
            
            if success:
                self.optimizer.log_performance("立即购买")
                return True
            
            console.print("❌ 未找到购买按钮", style="red")
            return False
            
        except Exception as e:
            console.print(f"❌ 购买点击失败: {e}", style="red")
            return False
    
    async def confirm_agreement(self) -> bool:
        """确认条款并下单"""
        try:
            console.print("📋 确认条款并下单...", style="yellow")
            self.optimizer.start_timer()
            
            # 并发执行勾选条款和点击确认
            operations = [
                {
                    'type': 'click',
                    'page': self.page,
                    'selector': SELECTORS['agreement_checkbox']
                },
                {
                    'type': 'wait',
                    'page': self.page,
                    'selector': SELECTORS['confirm_button']
                }
            ]
            
            results = await self.optimizer.batch_operation(operations)
            
            # 点击确认订单
            confirm_success = await self.optimizer.fast_click(
                self.page, SELECTORS['confirm_button']
            )
            
            if confirm_success:
                self.optimizer.log_performance("条款确认")
                return True
            
            return False
            
        except Exception as e:
            console.print(f"❌ 条款确认失败: {e}", style="red")
            return False
    
    async def run_speed_test(self) -> Dict[str, float]:
        """运行速度测试"""
        console.print("🏃‍♂️ 开始极速抢票测试...", style="bold cyan")
        
        total_start = time.time()
        results = {}
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TimeElapsedColumn(),
            console=console
        ) as progress:
            
            task = progress.add_task("抢票进行中...", total=6)
            
            # 1. 初始化浏览器
            if await self.initialize_browser():
                results['browser_init'] = self.optimizer.get_elapsed_time()
                progress.advance(task)
            else:
                return results
            
            # 2. 导航到页面
            if await self.navigate_to_target():
                results['navigation'] = self.optimizer.get_elapsed_time()
                progress.advance(task)
            else:
                return results
            
            # 3. 选择场次
            if await self.select_session():
                results['session_select'] = self.optimizer.get_elapsed_time()
                progress.advance(task)
            else:
                return results
            
            # 4. 选择票档
            if await self.select_ticket():
                results['ticket_select'] = self.optimizer.get_elapsed_time()
                progress.advance(task)
            else:
                return results
            
            # 5. 点击购买
            if await self.click_buy_now():
                results['buy_click'] = self.optimizer.get_elapsed_time()
                progress.advance(task)
            else:
                return results
            
            # 6. 确认订单
            if await self.confirm_agreement():
                results['confirm'] = self.optimizer.get_elapsed_time()
                progress.advance(task)
            
            results['total_time'] = (time.time() - total_start) * 1000
            
        return results
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

async def main():
    """主函数"""
    bot = GalaxyTicketBot()
    
    try:
        results = await bot.run_speed_test()
        
        # 显示结果
        console.print("\n🎉 抢票完成！性能报告:", style="bold green")
        for step, time_ms in results.items():
            console.print(f"  {step}: {time_ms:.1f}ms")
            
        if 'total_time' in results:
            total = results['total_time']
            if total < 3000:
                console.print(f"\n⚡ 总用时: {total:.1f}ms - 极速！", style="bold green")
            elif total < 5000:
                console.print(f"\n🚀 总用时: {total:.1f}ms - 很快！", style="bold yellow")
            else:
                console.print(f"\n🐌 总用时: {total:.1f}ms - 需要优化", style="bold red")
        
    except KeyboardInterrupt:
        console.print("\n⏹️  用户中断操作", style="yellow")
    except Exception as e:
        console.print(f"\n❌ 抢票失败: {e}", style="red")
    finally:
        await bot.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
