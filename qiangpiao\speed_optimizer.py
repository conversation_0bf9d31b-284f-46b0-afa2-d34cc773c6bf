"""
极速优化器 - 专门针对抢票速度优化的核心模块
实现毫秒级响应和并发处理
"""

import asyncio
import time
from typing import Optional, List, Dict, Any
from playwright.async_api import Page, <PERSON>rowser, BrowserContext
from fake_useragent import UserAgent
import logging
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()
logger = logging.getLogger(__name__)

class SpeedOptimizer:
    """极速优化器类 - 核心速度优化逻辑"""
    
    def __init__(self):
        self.ua = UserAgent()
        self.element_cache = {}  # 元素缓存
        self.selector_cache = {}  # 选择器缓存
        self.start_time = None
        
    async def optimize_page(self, page: Page) -> None:
        """页面级速度优化"""
        # 禁用不必要的资源加载
        await page.route("**/*.{png,jpg,jpeg,gif,svg,ico,woff,woff2}", 
                        lambda route: route.abort())
        await page.route("**/*.css", lambda route: route.abort())
        
        # 注入速度优化脚本
        await page.add_init_script("""
            // 禁用动画和过渡效果
            const style = document.createElement('style');
            style.textContent = `
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-delay: -0.01ms !important;
                    transition-duration: 0.01ms !important;
                    transition-delay: -0.01ms !important;
                }
            `;
            document.head.appendChild(style);
            
            // 加速定时器
            const originalSetTimeout = window.setTimeout;
            window.setTimeout = function(fn, delay) {
                return originalSetTimeout(fn, Math.min(delay, 10));
            };
        """)
        
    async def fast_wait_for_selector(self, page: Page, selector: str, 
                                   timeout: int = 1000) -> Optional[Any]:
        """极速选择器等待 - 使用缓存和并发"""
        cache_key = f"{page.url}_{selector}"
        
        # 检查缓存
        if cache_key in self.element_cache:
            try:
                element = self.element_cache[cache_key]
                if await element.is_visible():
                    return element
            except:
                # 缓存失效，清除
                del self.element_cache[cache_key]
        
        # 并发查找多个可能的选择器
        selectors = selector.split(', ')
        tasks = []
        
        for sel in selectors:
            task = asyncio.create_task(
                page.wait_for_selector(sel.strip(), timeout=timeout, state='visible')
            )
            tasks.append(task)
        
        try:
            # 返回第一个找到的元素
            done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
            
            # 取消其他任务
            for task in pending:
                task.cancel()
            
            # 获取结果
            for task in done:
                element = await task
                if element:
                    self.element_cache[cache_key] = element
                    return element
                    
        except Exception as e:
            logger.warning(f"快速选择器查找失败: {selector} - {e}")
            
        return None
    
    async def fast_click(self, page: Page, selector: str) -> bool:
        """极速点击 - 优化点击速度"""
        element = await self.fast_wait_for_selector(page, selector)
        if not element:
            return False
            
        try:
            # 使用JavaScript直接点击，比Playwright的click更快
            await page.evaluate("""
                (selector) => {
                    const element = document.querySelector(selector);
                    if (element) {
                        element.click();
                        return true;
                    }
                    return false;
                }
            """, selector.split(', ')[0])
            
            # 短暂等待确保点击生效
            await asyncio.sleep(0.05)
            return True
            
        except Exception as e:
            logger.error(f"快速点击失败: {selector} - {e}")
            return False
    
    async def batch_operation(self, operations: List[Dict[str, Any]]) -> List[bool]:
        """批量操作 - 并发执行多个操作"""
        tasks = []
        
        for op in operations:
            if op['type'] == 'click':
                task = asyncio.create_task(
                    self.fast_click(op['page'], op['selector'])
                )
            elif op['type'] == 'wait':
                task = asyncio.create_task(
                    self.fast_wait_for_selector(op['page'], op['selector'])
                )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return [r for r in results if not isinstance(r, Exception)]
    
    def start_timer(self) -> None:
        """开始计时"""
        self.start_time = time.time()
        
    def get_elapsed_time(self) -> float:
        """获取已用时间（毫秒）"""
        if self.start_time:
            return (time.time() - self.start_time) * 1000
        return 0
    
    async def smart_wait(self, page: Page, condition: str, max_wait: int = 500) -> bool:
        """智能等待 - 根据页面状态动态调整等待时间"""
        start = time.time()
        
        while (time.time() - start) * 1000 < max_wait:
            try:
                # 检查页面是否满足条件
                result = await page.evaluate(f"() => {condition}")
                if result:
                    return True
                    
                # 动态调整等待间隔
                elapsed = (time.time() - start) * 1000
                if elapsed < 100:
                    await asyncio.sleep(0.01)  # 10ms
                elif elapsed < 300:
                    await asyncio.sleep(0.05)  # 50ms
                else:
                    await asyncio.sleep(0.1)   # 100ms
                    
            except Exception:
                await asyncio.sleep(0.01)
                
        return False
    
    def log_performance(self, operation: str) -> None:
        """记录性能数据"""
        elapsed = self.get_elapsed_time()
        if elapsed < 1000:  # 小于1秒，绿色
            console.print(f"⚡ {operation}: {elapsed:.1f}ms", style="green")
        elif elapsed < 3000:  # 小于3秒，黄色
            console.print(f"⚠️  {operation}: {elapsed:.1f}ms", style="yellow")
        else:  # 超过3秒，红色
            console.print(f"🐌 {operation}: {elapsed:.1f}ms", style="red")
