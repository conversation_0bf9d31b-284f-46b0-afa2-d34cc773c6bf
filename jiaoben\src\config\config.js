/**
 * Galaxy Ticket Bot - 配置管理模块
 * 混合优化方案配置中心
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../.env') });

/**
 * 应用配置
 */
export const config = {
  // 基础配置
  app: {
    name: 'Galaxy Ticket Bot',
    version: '1.0.0',
    headless: process.env.HEADLESS === 'true',
    browserType: process.env.BROWSER_TYPE || 'chromium',
    maxRetries: parseInt(process.env.MAX_RETRIES) || 3,
    timeout: parseInt(process.env.TIMEOUT) || 30000,
  },

  // 代理配置
  proxy: {
    server: process.env.PROXY_SERVER || null,
    username: process.env.PROXY_USERNAME || null,
    password: process.env.PROXY_PASSWORD || null,
  },

  // 反检测配置
  antiDetection: {
    enableFingerprint: process.env.ENABLE_FINGERPRINT === 'true',
    enableStealth: process.env.ENABLE_STEALTH === 'true',
    deviceType: process.env.DEVICE_TYPE || 'desktop',
    osType: process.env.OS_TYPE || 'windows',
    locale: process.env.BROWSER_LOCALE || 'zh-CN,zh,en-US,en',
  },

  // 购票配置
  ticket: {
    targetUrl: process.env.TARGET_URL || 'https://www.galaxyticketing.com',
    quantity: parseInt(process.env.TICKET_QUANTITY) || 1,
    userInfo: {
      name: process.env.USER_NAME || '',
      email: process.env.USER_EMAIL || '',
      phone: process.env.USER_PHONE || '',
    },
  },

  // 高级配置
  advanced: {
    concurrentInstances: parseInt(process.env.CONCURRENT_INSTANCES) || 1,
    pageLoadWait: parseInt(process.env.PAGE_LOAD_WAIT) || 2000,
    elementTimeout: parseInt(process.env.ELEMENT_TIMEOUT) || 10000,
    saveScreenshots: process.env.SAVE_SCREENSHOTS === 'true',
    logLevel: process.env.LOG_LEVEL || 'info',
  },

  // 监控配置
  monitoring: {
    enabled: process.env.ENABLE_MONITORING === 'true',
    interval: parseInt(process.env.MONITORING_INTERVAL) || 5000,
    notifications: process.env.ENABLE_NOTIFICATIONS === 'true',
  },

  // Playwright 配置
  playwright: {
    launchOptions: {
      headless: process.env.HEADLESS === 'true',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
      ],
    },
    contextOptions: {
      viewport: { width: 1920, height: 1080 },
      userAgent: null, // 将由反检测模块动态设置
      locale: process.env.BROWSER_LOCALE?.split(',')[0] || 'zh-CN',
      timezoneId: 'Asia/Shanghai',
      permissions: ['geolocation', 'notifications'],
      geolocation: { latitude: 22.1987, longitude: 113.5439 }, // 澳门坐标
    },
  },

  // 指纹配置
  fingerprint: {
    // Fingerprint Suite 配置
    suite: {
      browsers: [
        { name: 'chrome', minVersion: 120, maxVersion: 128 },
        { name: 'firefox', minVersion: 115 },
      ],
      devices: [process.env.DEVICE_TYPE || 'desktop'],
      operatingSystems: [process.env.OS_TYPE || 'windows'],
      locales: process.env.BROWSER_LOCALE?.split(',') || ['zh-CN', 'zh', 'en-US', 'en'],
      httpVersion: '2',
    },
  },

  // 选择器配置 (针对Galaxy Ticketing网站)
  selectors: {
    // 这些选择器需要根据实际网站结构调整
    eventList: '.event-list .event-item',
    eventTitle: '.event-title',
    eventDate: '.event-date',
    selectButton: '.select-btn, .book-btn',
    quantityInput: 'input[name="quantity"], .quantity-input',
    quantityIncrease: '.quantity-plus, .qty-plus',
    quantityDecrease: '.quantity-minus, .qty-minus',
    userNameInput: 'input[name="name"], input[name="userName"], #name',
    userEmailInput: 'input[name="email"], input[name="userEmail"], #email',
    userPhoneInput: 'input[name="phone"], input[name="userPhone"], #phone',
    submitButton: '.submit-btn, .confirm-btn, button[type="submit"]',
    paymentButton: '.payment-btn, .pay-btn',
    successMessage: '.success-message, .order-success',
    errorMessage: '.error-message, .alert-error',
    loadingIndicator: '.loading, .spinner',
    captcha: '.captcha, .verification-code',
  },

  // 等待策略配置
  waitStrategies: {
    networkIdle: 'networkidle',
    domContentLoaded: 'domcontentloaded',
    load: 'load',
  },
};

/**
 * 验证配置
 */
export function validateConfig() {
  const errors = [];

  // 检查必需的配置
  if (!config.ticket.targetUrl) {
    errors.push('TARGET_URL 未配置');
  }

  if (config.advanced.concurrentInstances < 1) {
    errors.push('CONCURRENT_INSTANCES 必须大于 0');
  }

  if (config.ticket.quantity < 1) {
    errors.push('TICKET_QUANTITY 必须大于 0');
  }

  // 检查代理配置
  if (config.proxy.server && !config.proxy.server.includes(':')) {
    errors.push('PROXY_SERVER 格式错误，应为 host:port');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 获取运行时配置摘要
 */
export function getConfigSummary() {
  return {
    浏览器: config.app.browserType,
    无头模式: config.app.headless ? '是' : '否',
    反检测: {
      指纹伪装: config.antiDetection.enableFingerprint ? '启用' : '禁用',
      隐身模式: config.antiDetection.enableStealth ? '启用' : '禁用',
    },
    代理: config.proxy.server ? '已配置' : '未配置',
    并发数: config.advanced.concurrentInstances,
    目标网站: config.ticket.targetUrl,
  };
}

export default config;
