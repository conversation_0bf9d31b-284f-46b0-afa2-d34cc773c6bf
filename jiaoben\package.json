{"name": "galaxy-ticket-bot", "version": "1.0.0", "description": "高成功率Galaxy Ticketing购票脚本 - 混合优化方案", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --inspect src/index.js", "install-browsers": "npx playwright install chromium", "test-config": "node src/utils/test-config.js"}, "keywords": ["ticket", "automation", "playwright", "anti-detection", "galaxy-ticketing"], "author": "Galaxy Ticket Bo<PERSON>", "license": "MIT", "dependencies": {"playwright": "^1.47.0", "header-generator": "^2.1.47", "fingerprint-injector": "^2.1.0", "axios": "^1.7.4", "chalk": "^5.3.0", "inquirer": "^10.1.8", "ora": "^8.0.1", "dotenv": "^16.4.5", "winston": "^3.14.2", "node-cron": "^3.0.3", "user-agents": "^1.1.0"}, "devDependencies": {"@types/node": "^22.5.0", "nodemon": "^3.1.4"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/galaxy-ticket-bot.git"}, "bugs": {"url": "https://github.com/your-username/galaxy-ticket-bot/issues"}, "homepage": "https://github.com/your-username/galaxy-ticket-bot#readme"}