# 常用模式和最佳实践

- Nodriver连接失败的根本原因是Windows防火墙阻止localhost:9222端口连接，解决方案是添加Chrome程序和9222端口到防火墙例外。websockets版本需要降级到13.1以避免兼容性问题。
- Nodriver连接问题完整解决模式：1)诊断确认Windows防火墙阻止localhost:9222端口连接；2)以管理员权限添加Chrome程序和端口9222到防火墙例外；3)使用fix_nodriver_simple.bat一键修复脚本；4)验证Chrome调试端口连接；5)运行run_admin.bat启动程序。核心是防火墙域配置阻止问题，Chrome 139版本兼容性是次要因素。
- Playwright清理完成模式：1)删除browser_manager_fallback.py文件；2)恢复main.py使用原始browser_manager.py；3)清理requirements.txt中的playwright依赖；4)删除包含Playwright内容的文档；5)确保项目完全专注于Nodriver技术。项目现在是纯Nodriver实现，无任何fallback机制。
- Context7权威解决方案：Nodriver连接问题的根本原因是Windows沙盒模式问题。正确解决方案：1)使用sandbox=False而不是no_sandbox=True；2)添加--no-sandbox等Chrome参数；3)以管理员权限运行；4)避免Chrome 139版本。基于Context7官方文档和网络调研确认的权威方案。
- Nodriver官方解决方案：基于GitHub官方项目分析，推荐使用官方最简配置 await uc.start() 或带参数的 await uc.start(headless=False, browser_args=['--no-sandbox', '--disable-dev-shm-usage'])。避免复杂的多配置尝试，使用官方推荐的 uc.loop().run_until_complete(main()) 运行方式。项目地址：https://github.com/ultrafunkamsterdam/nodriver
